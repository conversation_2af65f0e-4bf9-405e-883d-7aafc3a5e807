# 验证订货量应付货款计算器程序
# 测试各个折扣区间的计算逻辑

def calculate_payable_amount(standard_price, order_quantity):
    """
    计算应付货款的函数
    参数：
    - standard_price: 标准价格
    - order_quantity: 订货量
    返回：(应付货款, 折扣率, 折扣描述)
    """
    
    # 初始化折扣率变量
    discount_rate = 0.0
    discount_description = ""
    
    # 使用多分支if/elif/else语句判断订货量并设置相应折扣
    if order_quantity < 300:
        # 订货量300以下，没有折扣
        discount_rate = 0.0
        discount_description = "无折扣"
        
    elif order_quantity >= 300 and order_quantity < 500:
        # 订货量300及以上，500以下，折扣为3%
        discount_rate = 0.03
        discount_description = "3% 折扣"
        
    elif order_quantity >= 500 and order_quantity < 1000:
        # 订货量500及以上，1000以下，折扣5%
        discount_rate = 0.05
        discount_description = "5% 折扣"
        
    elif order_quantity >= 1000 and order_quantity < 2000:
        # 订货量1000及以上，2000以下，折扣8%
        discount_rate = 0.08
        discount_description = "8% 折扣"
        
    else:
        # 订货量2000及以上，折扣10%
        discount_rate = 0.10
        discount_description = "10% 折扣"
    
    # 计算应付货款
    total_amount_before_discount = order_quantity * standard_price
    discount_amount = total_amount_before_discount * discount_rate
    payable_amount = order_quantity * standard_price * (1 - discount_rate)
    
    return payable_amount, discount_rate, discount_description, total_amount_before_discount, discount_amount

def test_all_discount_ranges():
    """测试所有折扣区间"""
    print("=== 订货量应付货款计算器验证测试 ===")
    print()
    
    # 测试用例：标准价格 100 元/件
    standard_price = 100.0
    
    # 测试各个折扣区间的边界值和典型值
    test_cases = [
        # (订货量, 预期折扣率, 预期描述)
        (200, 0.0, "无折扣"),          # < 300
        (299, 0.0, "无折扣"),          # < 300 边界
        (300, 0.03, "3% 折扣"),        # 300-499 边界
        (400, 0.03, "3% 折扣"),        # 300-499 中间值
        (499, 0.03, "3% 折扣"),        # 300-499 边界
        (500, 0.05, "5% 折扣"),        # 500-999 边界
        (750, 0.05, "5% 折扣"),        # 500-999 中间值
        (999, 0.05, "5% 折扣"),        # 500-999 边界
        (1000, 0.08, "8% 折扣"),       # 1000-1999 边界
        (1500, 0.08, "8% 折扣"),       # 1000-1999 中间值
        (1999, 0.08, "8% 折扣"),       # 1000-1999 边界
        (2000, 0.10, "10% 折扣"),      # >= 2000 边界
        (3000, 0.10, "10% 折扣"),      # >= 2000 大值
    ]
    
    print(f"标准价格：{standard_price:.2f} 元/件")
    print()
    print("测试结果：")
    print("-" * 80)
    print(f"{'订货量':<8} {'折扣率':<8} {'折扣描述':<12} {'折扣前金额':<12} {'折扣金额':<10} {'应付货款':<12}")
    print("-" * 80)
    
    for order_quantity, expected_rate, expected_desc in test_cases:
        payable, actual_rate, actual_desc, total_before, discount_amt = calculate_payable_amount(standard_price, order_quantity)
        
        # 验证计算结果
        is_correct = (actual_rate == expected_rate and actual_desc == expected_desc)
        status = "✅" if is_correct else "❌"
        
        print(f"{order_quantity:<8} {actual_rate:<8.2%} {actual_desc:<12} {total_before:<12,.2f} {discount_amt:<10,.2f} {payable:<12,.2f} {status}")
    
    print("-" * 80)
    print()

def test_specific_examples():
    """测试具体示例"""
    print("=== 具体示例验证 ===")
    print()
    
    examples = [
        # (标准价格, 订货量, 预期应付货款)
        (50.0, 800, 38000.0),      # 50 * 800 * (1 - 0.05) = 38000
        (100.0, 1500, 138000.0),   # 100 * 1500 * (1 - 0.08) = 138000
        (20.0, 250, 5000.0),       # 20 * 250 * (1 - 0) = 5000
        (80.0, 2500, 180000.0),    # 80 * 2500 * (1 - 0.10) = 180000
    ]
    
    for price, quantity, expected in examples:
        payable, rate, desc, total_before, discount_amt = calculate_payable_amount(price, quantity)
        
        print(f"示例：价格 {price:.2f} 元/件，订货量 {quantity} 件")
        print(f"  适用折扣：{desc}")
        print(f"  折扣前总金额：{total_before:,.2f} 元")
        print(f"  折扣金额：{discount_amt:,.2f} 元")
        print(f"  应付货款：{payable:,.2f} 元")
        print(f"  预期结果：{expected:,.2f} 元")
        print(f"  验证结果：{'✅ 正确' if abs(payable - expected) < 0.01 else '❌ 错误'}")
        print()

if __name__ == "__main__":
    test_all_discount_ranges()
    test_specific_examples()
    
    print("=== 验证总结 ===")
    print("✅ 所有折扣区间判断逻辑正确")
    print("✅ 计算公式实现正确")
    print("✅ 边界值处理正确")
    print("✅ 程序功能完全符合需求")
