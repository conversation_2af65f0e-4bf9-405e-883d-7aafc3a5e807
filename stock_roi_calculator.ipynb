# 股票投资回报率计算程序
# 作者：张家铭
# 功能：计算并格式化显示股票投资回报率

print("=== 股票投资回报率计算器 ===")
print()

# (1) 使用input()函数获取用户输入的股票信息
print("请输入股票相关信息：")

# 输入股票的初始价格
stock_price_initial = float(input("请输入股票的初始价格（元）："))

# 输入股票的当前价格
stock_price_current = float(input("请输入股票的当前价格（元）："))

# 输入购买的股数
stock_shares = int(input("请输入购买的股数（股）："))

print()
print("正在计算投资回报率...")
print()

# (2) 计算股票投资金额
# 投资金额 = 初始价格 * 购买的股数
investment_amount = stock_price_initial * stock_shares

print(f"计算投资金额：{stock_price_initial} × {stock_shares} = {investment_amount}")

# (3) 计算股票收益金额
# 收益金额 = 投资金额 * (当前价格 / 初始价格) - 投资金额
profit = investment_amount * (stock_price_current / stock_price_initial) - investment_amount

print(f"计算收益金额：{investment_amount} × ({stock_price_current} / {stock_price_initial}) - {investment_amount} = {profit}")

# (4) 计算股票投资回报率
# 投资回报率 = (收益金额 / 投资金额) * 100
roi = (profit / investment_amount) * 100

print(f"计算投资回报率：({profit} / {investment_amount}) × 100 = {roi}%")
print()

# (5) 使用format()方法格式化输出结果
print("=== 投资回报率计算结果 ===")
print()

# 投资金额：显示千分符并保留小数点后2位数
print("投资金额：{:,.2f}".format(investment_amount))

# 收益金额：显示千分符，保留小数点后2位数
print("收益金额：{:,.2f}".format(profit))

# 投资回报率：以百分比形式输出并且保留小数点后2位数
print("投资回报率：{:.2f}%".format(roi))

print()
print("计算完成！")