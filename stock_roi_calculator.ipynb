
print("请输入股票相关信息：")
stock_price_initial = float(input("请输入股票的初始价格（元）："))
stock_price_current = float(input("请输入股票的当前价格（元）："))
stock_shares = int(input("请输入购买的股数（股）："))
print("正在计算投资回报率...")
investment_amount = stock_price_initial * stock_shares
print(f"计算投资金额：{stock_price_initial} × {stock_shares} = {investment_amount}")
profit = investment_amount * (stock_price_current / stock_price_initial) - investment_amount
print(f"计算收益金额：{investment_amount} × ({stock_price_current} / {stock_price_initial}) - {investment_amount} = {profit}")
roi = (profit / investment_amount) * 100
print(f"计算投资回报率：({profit} / {investment_amount}) × 100 = {roi}%")
print("=== 投资回报率计算结果 ===")
print("投资金额：{:,.2f}".format(investment_amount))
print("收益金额：{:,.2f}".format(profit))
print("投资回报率：{:.2f}%".format(roi))
print("计算完成！")