# 个人所得税计算器
# 支持异常处理和循环输入，计算应纳税额和税后收入

def calculate_income_tax(annual_income):
    """
    计算个人所得税函数
    参数：annual_income - 年收入
    返回：(应纳税额, 税后收入)
    """
    # 年免征额
    tax_free_amount = 60000
    
    # 计算应纳税所得额
    taxable_income = annual_income - tax_free_amount
    
    # 如果应纳税所得额小于等于0，无需缴税
    if taxable_income <= 0:
        return 0, annual_income
    
    # 税率表：(上限, 税率, 速算扣除数)
    tax_brackets = [
        (36000, 0.03, 0),        # 级数1：不超过36,000元，3%，0
        (144000, 0.10, 2520),    # 级数2：超过36,000至144,000元，10%，2520
        (300000, 0.20, 16920),   # 级数3：超过144,000至300,000元，20%，16920
        (420000, 0.25, 31920),   # 级数4：超过300,000至420,000元，25%，31920
        (660000, 0.30, 52920),   # 级数5：超过420,000至660,000元，30%，52920
        (960000, 0.35, 85920),   # 级数6：超过660,000至960,000元，35%，85920
        (float('inf'), 0.45, 181920)  # 级数7：超过960,000元，45%，181920
    ]
    
    # 根据应纳税所得额确定税率和速算扣除数
    for upper_limit, tax_rate, deduction in tax_brackets:
        if taxable_income <= upper_limit:
            # 计算应纳税额：应纳税所得额 × 税率 - 速算扣除数
            tax_amount = taxable_income * tax_rate - deduction
            # 确保税额不为负数
            tax_amount = max(0, tax_amount)
            # 计算税后收入
            after_tax_income = annual_income - tax_amount
            return tax_amount, after_tax_income

def main():
    """
    主程序函数，处理用户输入和异常
    """
    print("=== 个人所得税计算器 ===")
    print("年免征额：60,000元")
    print("输入'quit'或'q'退出程序")
    print("-" * 40)
    
    while True:
        try:
            # 获取用户输入
            user_input = input("\n请输入年收入（元）：").strip()
            
            # 检查是否退出
            if user_input.lower() in ['quit', 'q', '退出']:
                print("感谢使用个人所得税计算器！")
                break
            
            # 转换为浮点数
            annual_income = float(user_input)
            
            # 检查输入是否为负数
            if annual_income < 0:
                print("❌ 年收入不能为负数，请重新输入！")
                continue
            
            # 调用计算函数
            tax_amount, after_tax_income = calculate_income_tax(annual_income)
            
            # 输出计算结果
            print(f"\n=== 计算结果 ===")
            print(f"年收入：{annual_income:,.2f} 元")
            print(f"年免征额：60,000.00 元")
            print(f"应纳税所得额：{max(0, annual_income - 60000):,.2f} 元")
            print(f"应纳税额：{tax_amount:,.2f} 元")
            print(f"税后收入：{after_tax_income:,.2f} 元")
            
            # 计算税率
            if annual_income > 0:
                effective_tax_rate = (tax_amount / annual_income) * 100
                print(f"实际税率：{effective_tax_rate:.2f}%")
            
        except ValueError:
            # 处理输入格式错误
            print("❌ 输入格式错误！请输入有效的数字。")
        except KeyboardInterrupt:
            # 处理Ctrl+C中断
            print("\n\n程序被用户中断，再见！")
            break
        except Exception as e:
            # 处理其他异常
            print(f"❌ 发生未知错误：{e}")

# 运行主程序
if __name__ == "__main__":
    main()