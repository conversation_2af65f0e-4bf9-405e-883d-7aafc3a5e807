# 经济学1班成绩管理系统
# 对学生成绩字典进行各种操作

# 创建经济学1班学生成绩字典
economics_trade = {
    "202210105035": {"姓名": "朱燕柳", "成绩": 85},
    "202210105097": {"姓名": "叶自雅", "成绩": 92},
    "202210137004": {"姓名": "罗锦昊", "成绩": 78},
    "202210137006": {"姓名": "容燕萍", "成绩": 89},
    "202210137010": {"姓名": "倪诗雨", "成绩": 76},
    "202210137012": {"姓名": "杨阳", "成绩": 94},
    "202210137013": {"姓名": "李冰梅", "成绩": 82},
    "202210137015": {"姓名": "陈茵茵", "成绩": 90},
    "202210137017": {"姓名": "李素芬", "成绩": 79},
    "202210137018": {"姓名": "杨弈昊", "成绩": 87},
    "202210137019": {"姓名": "林华鑫", "成绩": 95},
    "202210137021": {"姓名": "盘格玲", "成绩": 81},
    "202210137024": {"姓名": "李淑菲", "成绩": 93},
    "202210137026": {"姓名": "陈慧琦", "成绩": 86},
    "202210137028": {"姓名": "陆冠荣", "成绩": 77},
    "202210137029": {"姓名": "冯婉怡", "成绩": 91},
    "202210137030": {"姓名": "方腾", "成绩": 84},
    "202210137033": {"姓名": "王睿", "成绩": 96},
    "202210137036": {"姓名": "何昕潞", "成绩": 80},
    "202210137037": {"姓名": "陈嘉欣", "成绩": 97},
    "202210137039": {"姓名": "洪宁", "成绩": 88},
    "202210137040": {"姓名": "莫诗雅", "成绩": 75},
    "202210137042": {"姓名": "麦雯静", "成绩": 99},
    "202210137044": {"姓名": "郑乐琪", "成绩": 83},
    "202210137048": {"姓名": "陈芊卉", "成绩": 98},
    "202210137049": {"姓名": "韦雨彤", "成绩": 85},
    "202210137051": {"姓名": "刘锦妍", "成绩": 74},
    "202210137055": {"姓名": "杨一鸣", "成绩": 90},
    "202210137056": {"姓名": "赵富乾", "成绩": 82},
    "202210137059": {"姓名": "葛罗明", "成绩": 91},
    "202210137061": {"姓名": "张宇森", "成绩": 87},
    "202210137065": {"姓名": "赵巧", "成绩": 76},
    "202210137068": {"姓名": "廖俊辉", "成绩": 93},
    "202210137070": {"姓名": "毛梓航", "成绩": 89},
    "202310137067": {"姓名": "张某某", "成绩": 88},
    "202310137068": {"姓名": "李某某", "成绩": 92},
}

print("=== 经济学1班成绩管理系统 ===")

# (1) 修改张某某的成绩状态为"缺考"
economics_trade["202310137067"]["成绩"] = "缺考"
print("(1) 已将张某某的成绩修改为：缺考")

# (2) 将本人(张家铭)的成绩修改为100
economics_trade["202210137004"]["姓名"] = "张家铭"  # 假设使用罗锦昊的学号
economics_trade["202210137004"]["成绩"] = 100
print("(2) 已将张家铭的成绩修改为：100")

# (3) 删除李某某的姓名及成绩
del economics_trade["202310137068"]
print("(3) 已删除李某某的信息")

# (4) 使用for循环遍历并输出当前字典
print("\n(4) 当前班级学生信息：")
print(f"{'学号':<15} {'姓名':<8} {'成绩':<6}")
print("-" * 35)
for student_id, info in economics_trade.items():
    print(f"{student_id:<15} {info['姓名']:<8} {info['成绩']:<6}")

# (5) 统计当前总人数并输出
total_students = len(economics_trade)
print(f"\n(5) 当前班级总人数：{total_students} 人")

# (6) 从键盘输入学号，显示该同学的成绩
print("\n(6) 查询学生成绩：")
search_id = input("请输入学号：")
if search_id in economics_trade:
    student_info = economics_trade[search_id]
    print(f"学号：{search_id}，姓名：{student_info['姓名']}，成绩：{student_info['成绩']}")
else:
    print("没找到该同学")

# (7) 统计全班80分以上的同学有多少名
high_score_count = 0
for student_id, info in economics_trade.items():
    # 判断成绩是否为数字且大于等于80
    if isinstance(info['成绩'], (int, float)) and info['成绩'] >= 80:
        high_score_count += 1

print(f"\n(7) 全班80分以上的同学有：{high_score_count} 名")
print("\n=== 操作完成 ===")