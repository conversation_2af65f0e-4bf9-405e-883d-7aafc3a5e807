# 商品销售记录程序
# 使用while循环输入商品信息，保存到列表并写入文件

print("=== 商品销售记录程序 ===")
print("请输入商品名称和销售额（格式：商品名,销售额）")
print("输入'exit'结束录入")
print("-" * 50)

# (1) 初始化销售记录列表
sales = []  # 存储商品名称和销售额的列表

# (1) 使用while循环输入商品名称和销售额
while True:
    # 获取用户输入
    user_input = input("请输入商品名称和销售额（商品名,销售额）：").strip()
    
    # (2) 使用if和break语句以exit为结束循环标识字符
    if user_input.lower() == 'exit':
        print("结束录入")
        break
    
    # 检查输入格式是否正确
    if ',' not in user_input:
        print("❌ 输入格式错误！请使用格式：商品名,销售额")
        continue
    
    try:
        # 分割商品名称和销售额
        parts = user_input.split(',')
        if len(parts) != 2:
            print("❌ 输入格式错误！请使用格式：商品名,销售额")
            continue
        
        product_name = parts[0].strip()  # 商品名称
        sales_amount = float(parts[1].strip())  # 销售额（转换为浮点数）
        
        # 检查销售额是否为正数
        if sales_amount < 0:
            print("❌ 销售额不能为负数！")
            continue
        
        # 将商品信息添加到销售列表中
        sales.append((product_name, sales_amount))
        print(f"✅ 已添加：{product_name} - {sales_amount}元")
        
    except ValueError:
        print("❌ 销售额必须是数字！")
        continue

# 显示录入的商品信息
print(f"\n共录入 {len(sales)} 件商品：")
print(f"{'序号':<4} {'商品名称':<12} {'销售额':<10}")
print("-" * 30)
for i, (name, amount) in enumerate(sales, 1):
    print(f"{i:<4} {name:<12} {amount:<10.2f}")

# (3) 使用for循环和文件读写方式将数据存储到文件
if sales:  # 如果有销售记录
    try:
        # 创建并写入sales.txt文件
        with open('sales.txt', 'w', encoding='utf-8') as file:
            # 写入文件标题
            file.write("=== 当天商品销售明细 ===\n")
            file.write(f"{'序号':<4} {'商品名称':<12} {'销售额(元)':<12}\n")
            file.write("-" * 35 + "\n")
            
            # 初始化总收入
            total_revenue = 0
            
            # 使用for循环遍历销售列表，写入每个商品的信息
            for i, (product_name, sales_amount) in enumerate(sales, 1):
                # 写入商品明细
                file.write(f"{i:<4} {product_name:<12} {sales_amount:<12.2f}\n")
                # 累计总收入
                total_revenue += sales_amount
            
            # 写入分隔线和总收入
            file.write("-" * 35 + "\n")
            file.write(f"当天总收入：{total_revenue:.2f}元\n")
            file.write(f"商品种类：{len(sales)}种\n")
            
        print(f"\n✅ 销售记录已保存到 sales.txt 文件")
        print(f"当天总收入：{total_revenue:.2f}元")
        print(f"商品种类：{len(sales)}种")
        
        # 读取并显示文件内容进行验证
        print("\n=== sales.txt 文件内容 ===")
        with open('sales.txt', 'r', encoding='utf-8') as file:
            content = file.read()
            print(content)
            
    except Exception as e:
        print(f"❌ 保存文件时发生错误：{e}")
else:
    print("\n没有录入任何商品信息")

print("\n程序执行完成！请手动打开 sales.txt 文件查看内容。")