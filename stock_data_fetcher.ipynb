# 股票数据获取及文件存储程序
# 使用Baostock获取股票历史K线数据并保存到Excel文件

# (1) 导入必要的库
import baostock as bs
import pandas as pd
import datetime
import random

print("=== 股票数据获取及文件存储程序 ===")
print("使用Baostock获取沪深A股历史K线数据")
print("-" * 50)

# 登录baostock系统
print("正在登录Baostock系统...")
lg = bs.login()

# 验证登录结果
if lg.error_code == '0':
    print("✅ Baostock登录成功")
else:
    print(f"❌ Baostock登录失败：{lg.error_msg}")
    exit()

try:
    # 设置股票代码（示例：平安银行）
    stock_code = "sz.000001"  # 平安银行
    stock_name = "平安银行"
    
    # 生成随机10天的日期范围
    # 设置结束日期为最近的交易日
    end_date = datetime.date(2024, 1, 19)  # 设置一个固定的结束日期
    
    # 计算开始日期（往前推15个自然日，确保包含10个交易日）
    start_date = end_date - datetime.timedelta(days=15)
    
    # 转换为字符串格式
    start_date_str = start_date.strftime('%Y-%m-%d')
    end_date_str = end_date.strftime('%Y-%m-%d')
    
    print(f"\n股票代码：{stock_code}（{stock_name}）")
    print(f"查询日期范围：{start_date_str} 至 {end_date_str}")
    
    # (2) 使用bs.query_history_k_data_plus函数获取历史K线数据
    print("\n正在获取历史K线数据...")
    
    # 查询历史K线数据
    rs = bs.query_history_k_data_plus(
        stock_code,
        "date,code,open,high,low,close,preclose,volume,amount,adjustflag,turn,tradestatus,pctChg,isST",
        start_date=start_date_str,
        end_date=end_date_str,
        frequency="d",  # 日K线
        adjustflag="3"  # 不复权
    )
    
    # 检查查询结果
    if rs.error_code != '0':
        print(f"❌ 数据查询失败：{rs.error_msg}")
    else:
        print("✅ 数据查询成功")
        
        # (3) 将数据转换为DataFrame格式
        print("\n正在处理数据...")
        
        # 获取数据列表
        data_list = []
        while (rs.error_code == '0') & rs.next():
            data_list.append(rs.get_row_data())
        
        # 转换为DataFrame
        result = pd.DataFrame(data_list, columns=rs.fields)
        
        # 检查数据是否为空
        if result.empty:
            print("❌ 未获取到数据，可能是非交易日期间")
        else:
            print(f"✅ 成功获取 {len(result)} 条数据")
            
            # 数据类型转换和处理
            numeric_columns = ['open', 'high', 'low', 'close', 'preclose', 'volume', 'amount', 'turn', 'pctChg']
            for col in numeric_columns:
                if col in result.columns:
                    result[col] = pd.to_numeric(result[col], errors='coerce')
            
            # 添加中文列名
            result_cn = result.copy()
            column_mapping = {
                'date': '日期',
                'code': '股票代码', 
                'open': '开盘价',
                'high': '最高价',
                'low': '最低价',
                'close': '收盘价',
                'preclose': '前收盘价',
                'volume': '成交量',
                'amount': '成交额',
                'turn': '换手率',
                'pctChg': '涨跌幅',
                'tradestatus': '交易状态',
                'isST': '是否ST'
            }
            
            # 重命名列
            result_cn.rename(columns=column_mapping, inplace=True)
            
            # 显示数据预览
            print("\n数据预览（前5行）：")
            print(result_cn[['日期', '开盘价', '最高价', '最低价', '收盘价', '成交量', '涨跌幅']].head())
            
            # (4) 将数据保存到Excel文件
            print("\n正在保存数据到文件...")
            
            # 生成文件名
            filename = f"{stock_name}_{stock_code.replace('.', '_')}_{start_date_str}_to_{end_date_str}.xlsx"
            
            # 保存到Excel文件
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                # 保存原始数据（英文列名）
                result.to_excel(writer, sheet_name='原始数据', index=False)
                
                # 保存中文数据（中文列名）
                result_cn.to_excel(writer, sheet_name='中文数据', index=False)
                
                # 创建数据统计表
                stats_data = {
                    '统计项目': ['数据条数', '日期范围', '最高价', '最低价', '平均收盘价', '总成交量', '平均涨跌幅'],
                    '数值': [
                        len(result),
                        f"{result['date'].min()} 至 {result['date'].max()}",
                        f"{result['high'].max():.2f}",
                        f"{result['low'].min():.2f}",
                        f"{result['close'].mean():.2f}",
                        f"{result['volume'].sum():,.0f}",
                        f"{result['pctChg'].mean():.2f}%"
                    ]
                }
                stats_df = pd.DataFrame(stats_data)
                stats_df.to_excel(writer, sheet_name='数据统计', index=False)
            
            print(f"✅ 数据已成功保存到文件：{filename}")
            print(f"文件包含3个工作表：原始数据、中文数据、数据统计")
            
            # 显示数据统计信息
            print("\n=== 数据统计信息 ===")
            print(f"股票名称：{stock_name}")
            print(f"股票代码：{stock_code}")
            print(f"数据条数：{len(result)} 条")
            print(f"日期范围：{result['date'].min()} 至 {result['date'].max()}")
            print(f"最高价：{result['high'].max():.2f} 元")
            print(f"最低价：{result['low'].min():.2f} 元")
            print(f"平均收盘价：{result['close'].mean():.2f} 元")
            print(f"总成交量：{result['volume'].sum():,.0f} 股")
            print(f"平均涨跌幅：{result['pctChg'].mean():.2f}%")

except Exception as e:
    print(f"❌ 程序执行出错：{str(e)}")

finally:
    # 退出baostock系统
    print("\n正在退出Baostock系统...")
    bs.logout()
    print("✅ 已安全退出Baostock系统")
    print("\n程序执行完成！")