# 测试去掉冗余信息后的订货量应付货款计算器
# 验证程序简洁性和功能完整性

def simulate_final_calculator(standard_price, order_quantity):
    """模拟最终优化后的计算器"""
    
    print("=== 订货量应付货款计算器 ===")
    print("折扣规则：")
    print("• 订货量 < 300：无折扣（0%）")
    print("• 订货量 300-499：折扣 3%")
    print("• 订货量 500-999：折扣 5%")
    print("• 订货量 1000-1999：折扣 8%")
    print("• 订货量 ≥ 2000：折扣 10%")
    
    # (1) 模拟用户输入
    print("请输入订货信息：")
    print(f"模拟输入 - 标准价格：{standard_price:.2f} 元/件")
    print(f"模拟输入 - 订货量：{order_quantity} 件")
    
    print(f"标准价格：{standard_price:.2f} 元/件")
    print(f"订货量：{order_quantity} 件")
    
    # (2) 根据订货量大小，使用多分支if/elif/else语句计算折扣
    print("正在计算折扣...")
    
    # 初始化折扣率变量
    discount_rate = 0.0
    discount_description = ""
    
    # 使用多分支if/elif/else语句判断订货量并设置相应折扣
    if order_quantity < 300:
        discount_rate = 0.0
        discount_description = "无折扣"
        print(f"订货量 {order_quantity} 件 < 300 件，{discount_description}")
    elif order_quantity >= 300 and order_quantity < 500:
        discount_rate = 0.03
        discount_description = "3% 折扣"
        print(f"订货量 {order_quantity} 件，300-499 件区间，享受 {discount_description}")
    elif order_quantity >= 500 and order_quantity < 1000:
        discount_rate = 0.05
        discount_description = "5% 折扣"
        print(f"订货量 {order_quantity} 件，500-999 件区间，享受 {discount_description}")
    elif order_quantity >= 1000 and order_quantity < 2000:
        discount_rate = 0.08
        discount_description = "8% 折扣"
        print(f"订货量 {order_quantity} 件，1000-1999 件区间，享受 {discount_description}")
    else:
        discount_rate = 0.10
        discount_description = "10% 折扣"
        print(f"订货量 {order_quantity} 件 ≥ 2000 件，享受 {discount_description}")
    
    # 计算应付货款
    total_amount_before_discount = order_quantity * standard_price
    discount_amount = total_amount_before_discount * discount_rate
    payable_amount = order_quantity * standard_price * (1 - discount_rate)
    
    # (3) 使用f语句或format()格式化方法及print()函数输出相应的货款
    print("=== 订货量应付货款计算结果 ===")
    
    # 使用f字符串格式化输出
    print(f"标准价格：{standard_price:.2f} 元/件")
    print(f"订货量：{order_quantity:,} 件")
    print(f"适用折扣：{discount_description}")
    print(f"折扣率：{discount_rate:.2%}")
    
    # 使用format()方法格式化输出
    print("折扣前总金额：{:,.2f} 元".format(total_amount_before_discount))
    print("折扣金额：{:,.2f} 元".format(discount_amount))
    print("应付货款：{:,.2f} 元".format(payable_amount))
    
    # 计算节省金额
    savings = discount_amount
    if savings > 0:
        print(f"恭喜！您节省了：{savings:,.2f} 元")
        savings_percentage = (savings / total_amount_before_discount) * 100
        print(f"节省比例：{savings_percentage:.2f}%")
    else:
        print("本次订货无折扣优惠")
    
    print("计算完成！感谢您的订货！")
    
    return payable_amount, discount_rate, discount_description

def test_optimized_calculator():
    """测试优化后的计算器"""
    print("=== 测试去掉冗余信息后的计算器 ===")
    print("✅ 去掉了重复的计算过程显示")
    print("✅ 保留了最终结果的专业格式化输出")
    print("✅ 程序更加简洁清晰")
    print("=" * 50)
    
    # 测试一个典型用例
    print("\n【测试用例】")
    print("-" * 30)
    payable, rate, desc = simulate_final_calculator(100.0, 1500)
    
    print("\n" + "=" * 50)
    print("🎉 优化完成！")
    print("✅ 去掉了冗余的计算过程显示")
    print("✅ 保留了完整的功能和专业的输出格式")
    print("✅ 程序更加简洁，用户体验更好")

if __name__ == "__main__":
    test_optimized_calculator()
