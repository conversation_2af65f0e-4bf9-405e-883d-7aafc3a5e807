# 验证钞票兑换方案计算器
def test_money_exchange():
    print("=== 100元钞票兑换方案验证 ===")
    print("可兑换面值：100元、50元、20元、10元")
    print("方案编号 100元 50元 20元 10元 总金额")
    print("-" * 40)
    
    total_money = 100
    scheme_count = 0
    
    # 使用四重for循环遍历所有可能的组合
    for count_100 in range(total_money // 100 + 1):  # 100元张数：0-1张
        for count_50 in range((total_money - count_100 * 100) // 50 + 1):  # 50元张数
            for count_20 in range((total_money - count_100 * 100 - count_50 * 50) // 20 + 1):  # 20元张数
                # 计算剩余金额
                remaining = total_money - count_100 * 100 - count_50 * 50 - count_20 * 20
                
                # 使用if语句判断剩余金额是否能被10整除
                if remaining >= 0 and remaining % 10 == 0:
                    count_10 = remaining // 10  # 计算10元张数
                    scheme_count += 1  # 方案数量加1
                    
                    # 验证总金额
                    total_check = count_100 * 100 + count_50 * 50 + count_20 * 20 + count_10 * 10
                    
                    # 输出兑换方案
                    print(f"方案{scheme_count:>3}   {count_100:>3}   {count_50:>2}   {count_20:>2}   {count_10:>2}   {total_check:>3}元")\
    
    print("-" * 40)
    print(f"总共有 {scheme_count} 种兑换方案")
    
    # 手动验证几个典型方案
    print("\n典型方案验证：")
    print("方案1: 1×100 = 100元 ✅")
    print("方案2: 2×50 = 100元 ✅") 
    print("方案3: 1×50 + 1×20 + 3×10 = 50+20+30 = 100元 ✅")
    print("方案4: 1×50 + 2×20 + 1×10 = 50+40+10 = 100元 ✅")
    print("方案11: 10×10 = 100元 ✅")

if __name__ == "__main__":
    test_money_exchange()
