# 闰年判断程序
# 规则：能被4整除但不能被100整除，或能被400整除

def is_leap_year(year):
    """
    判断是否为闰年的函数
    参数：year - 年份（整数）
    返回：True表示闰年，False表示平年
    """
    # 使用闰年判断公式
    return (year % 4 == 0 and year % 100 != 0) or (year % 400 == 0)

print("=== 闰年判断程序 ===")
print("闰年规则：能被4整除但不能被100整除，或能被400整除")

# 使用input()函数输入年份
year = int(input("请输入年份："))

# 调用函数判断是否为闰年
if is_leap_year(year):
    result = "是闰年"
else:
    result = "不是闰年"

# 输出判断结果
print(f"{year}年{result}")
