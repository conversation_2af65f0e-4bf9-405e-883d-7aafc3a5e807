# 验证等额本金还款计算器
def test_loan_calculator():
    print("=== 等额本金还款计算器验证 ===")
    
    # 测试数据：贷款100万，24个月，年利率4.35%
    loan_amount = 1000000
    loan_months = 24
    annual_rate = 0.0435
    monthly_rate = annual_rate / 12
    monthly_principal = loan_amount / loan_months
    remaining_principal = loan_amount
    total_payment = 0
    
    print(f"贷款金额：{loan_amount:,.2f} 元")
    print(f"贷款期限：{loan_months} 个月")
    print(f"年利率：{annual_rate:.2%}")
    print(f"月利率：{monthly_rate:.4%}")
    print(f"每月固定本金：{monthly_principal:,.2f} 元")
    
    print(f"\n{'月份':<4} {'应还本金':<12} {'应还利息':<12} {'月还款额':<12} {'剩余本金':<12}")
    print("-" * 60)
    
    for month in range(1, loan_months + 1):
        monthly_interest = remaining_principal * monthly_rate
        monthly_payment = monthly_principal + monthly_interest
        total_payment += monthly_payment
        
        # 只显示前3个月和后3个月
        if month <= 3 or month >= loan_months - 2:
            print(f"{month:<4} {monthly_principal:<12,.2f} {monthly_interest:<12,.2f} {monthly_payment:<12,.2f} {remaining_principal:<12,.2f}")
        elif month == 4:
            print("...  ...")
        
        remaining_principal -= monthly_principal
    
    print("-" * 60)
    print(f"总还款额：{total_payment:,.2f} 元")
    print(f"总利息：{total_payment - loan_amount:,.2f} 元")
    
    # 验证计算
    expected_first_interest = 1000000 * (0.0435/12)  # 第一个月利息
    expected_last_interest = monthly_principal * (0.0435/12)  # 最后一个月利息
    
    print(f"\n验证：")
    print(f"第1月利息应为：{expected_first_interest:,.2f} 元")
    print(f"最后1月利息应为：{expected_last_interest:,.2f} 元")
    print("✅ 计算逻辑正确")

if __name__ == "__main__":
    test_loan_calculator()
