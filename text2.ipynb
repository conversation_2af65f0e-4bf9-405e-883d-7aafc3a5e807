{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Looking in indexes: https://pypi.mirrors.ustc.edu.cn/simple\n", "Requirement already satisfied: baostock in ./venv/lib/python3.12/site-packages (0.8.9)\n", "Requirement already satisfied: pandas>=0.18.0 in ./venv/lib/python3.12/site-packages (from baostock) (2.2.3)\n", "Requirement already satisfied: numpy>=1.26.0 in ./venv/lib/python3.12/site-packages (from pandas>=0.18.0->baostock) (2.2.3)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in ./venv/lib/python3.12/site-packages (from pandas>=0.18.0->baostock) (2.9.0.post0)\n", "Requirement already satisfied: pytz>=2020.1 in ./venv/lib/python3.12/site-packages (from pandas>=0.18.0->baostock) (2025.1)\n", "Requirement already satisfied: tzdata>=2022.7 in ./venv/lib/python3.12/site-packages (from pandas>=0.18.0->baostock) (2025.1)\n", "Requirement already satisfied: six>=1.5 in ./venv/lib/python3.12/site-packages (from python-dateutil>=2.8.2->pandas>=0.18.0->baostock) (1.17.0)\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["pip install baostock\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"ename": "SyntaxError", "evalue": "invalid syntax (1201407667.py, line 1)", "output_type": "error", "traceback": ["\u001b[0;36m  Cell \u001b[0;32mIn[1], line 1\u001b[0;36m\u001b[0m\n\u001b[0;31m    1.原始字符串\u001b[0m\n\u001b[0m      ^\u001b[0m\n\u001b[0;31mSyntaxError\u001b[0m\u001b[0;31m:\u001b[0m invalid syntax\n"]}], "source": [" 1.原始字符串  \n", "s = \"*** Hello, <PERSON>! \\n This is a string example. ***\" \n", "# 使用输出函数print(s) \n", "print(s) "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["中钢国际在2021年3月1日的涨跌幅10.10309278350516\n"]}], "source": ["stock_prices_end = 5.34\n", "stock_prices_start = 4.85\n", "stock_zhang = (stock_prices_end - stock_prices_start) / stock_prices_start * 100\n", "print(f\"中钢国际在2021年3月1日的涨跌幅{stock_zhang}\")"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.42857142857142855\n", "7.714285714285714\n"]}], "source": ["math_1 = 2\n", "math_2 = 10\n", "math_3 = 5\n", "math_4 = 3\n", "math_5 = 7\n", "math_6 = 16\n", "end_1 = (math_1**math_4+math_2-math_3*math_4)/math_5\n", "end_2 = (math_1+math_3**math_1)*(math_6%math_5)/math_5\n", "print(end_1)\n", "print(end_2)\n", "\n"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/plain": ["'19790716'"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["idCard = '520125197907167551'\n", "idCard[6:14]"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"ename": "SyntaxError", "evalue": "invalid character '：' (U+FF1A) (3532037033.py, line 9)", "output_type": "error", "traceback": ["\u001b[0;36m  Cell \u001b[0;32mIn[25], line 9\u001b[0;36m\u001b[0m\n\u001b[0;31m    李某某同学：\u001b[0m\n\u001b[0m         ^\u001b[0m\n\u001b[0;31mSyntaxError\u001b[0m\u001b[0;31m:\u001b[0m invalid character '：' (U+FF1A)\n"]}], "source": ["student_id=\"202310100505\"\n", "student_name=\"李某某\"\n", "college=\"经济学院\"\n", "department=\"金融工程\"\n", "check_in_time=\"二〇二三年九月十日\"\n", "student_id, student_name = \"202310100505\", \"李某某\"\n", "\n", "\n", "李某某同学：\n", "我校决定录取你入读经济学院金融工程易专业 学号:202310100505。请你凭本通知书于二0二三年九月十日到校报到。\n", "广州城市理工学院 校长：苏成\n", "print(\"姓名：\" + student_name+\"同学：\")\n", "print(\"我校决定将你录取入读\" + college + department + \"专业，学号：\" + student_id + \"。请你凭本通知书于\" + check_in_time + \"到校报道\" )\n", "print(\"广州城市理工学院 校长：苏成\")\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"ename": "SyntaxError", "evalue": "invalid syntax (1201407667.py, line 1)", "output_type": "error", "traceback": ["\u001b[0;36m  Cell \u001b[0;32mIn[2], line 1\u001b[0;36m\u001b[0m\n\u001b[0;31m    1.原始字符串\u001b[0m\n\u001b[0m      ^\u001b[0m\n\u001b[0;31mSyntaxError\u001b[0m\u001b[0;31m:\u001b[0m invalid syntax\n"]}], "source": [" 1.原始字符串  \n", "s = \"*** Hello, <PERSON>! \\n This is a string example. ***\" \n", "# 使用输出函数print(s) \n", "print(s) "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["*** Hello, <PERSON>! \n", " This is a string example. ***\n"]}], "source": [" \n", "s = \"*** Hello, <PERSON>! \\n This is a string example. ***\" \n", "# 使用输出函数print(s) \n", "print(s) "]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["49\n"]}], "source": ["s = \"*** Hello, <PERSON>! \\n This is a string example. ***\" \n", "print(len(s))\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["*** HELLO, WORLD! \n", " THIS IS A STRING EXAMPLE. ***\n"]}], "source": ["s = \"*** Hello, <PERSON>! \\n This is a string example. ***\" \n", "print(s.upper())"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["*** hello, world! \n", " this is a string example. ***\n"]}], "source": ["s = \"*** Hello, <PERSON>! \\n This is a string example. ***\" \n", "print(s.lower())"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["11\n"]}], "source": ["s = \"*** Hello, <PERSON>! \\n This is a string example. ***\" \n", "print(s.find(\"World\"))"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["*** Hello, <PERSON>! \n", " This is a string example. ***\n"]}], "source": ["s = \"*** Hello, <PERSON>! \\n This is a string example. ***\" \n", "print(s.replace(\"World\", \"Python\"))"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['***', 'Hello,', 'World!', 'This', 'is', 'a', 'string', 'example.', '***']\n"]}], "source": ["s = \"*** Hello, <PERSON>! \\n This is a string example. ***\" \n", "print(s.split())"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" Hello, <PERSON>! \n", " This is a string example. \n"]}], "source": ["s = \"*** Hello, <PERSON>! \\n This is a string example. ***\" \n", "print(s.strip(\"*\"))"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["False\n"]}], "source": ["s = \"*** Hello, <PERSON>! \\n This is a string example. ***\" \n", "print(s.startswith(\"Hello\"))"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True\n"]}], "source": ["s = \"*** Hello, <PERSON>! \\n This is a string example. ***\" \n", "print(s.endswith(\"***\"))"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["rld! \n"]}], "source": ["print(s[13:18])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["rld! \n"]}], "source": ["print(s[13:18])"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["*** Hello, <PERSON>! \n", " This is a string example. *** Python非常有趣\n", "*** Hello, <PERSON>! \n", " This is a string example. *** Python非常有趣\n"]}], "source": ["new_s = \"Python非常有趣\"\n", "print(f\"{s} {new_s}\")\n", "print(s + \" \" + new_s)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"ename": "SyntaxError", "evalue": "invalid syntax (*********.py, line 1)", "output_type": "error", "traceback": ["\u001b[0;36m  Cell \u001b[0;32mIn[18], line 1\u001b[0;36m\u001b[0m\n\u001b[0;31m    python -m venv venv\u001b[0m\n\u001b[0m              ^\u001b[0m\n\u001b[0;31mSyntaxError\u001b[0m\u001b[0;31m:\u001b[0m invalid syntax\n"]}], "source": ["python -m venv venv"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["每月还款金额为：5307.27元\n"]}], "source": ["total = float(input(\"请输入贷款总金额：\"))\n", "terms = int(input(\"请输入贷款期限（月）：\"))\n", "rate_year=0.049\n", "rate_month=rate_year/12\n", "result = total * rate_month * (1 + rate_month) ** terms / ((1 + rate_month) ** terms - 1)\n", "print(\"每月还款金额为：{:.2f}元\".format(result))\n"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 2}