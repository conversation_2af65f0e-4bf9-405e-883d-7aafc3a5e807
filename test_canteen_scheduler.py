# 验证学校饭堂随机安排程序
import random

def arrange_canteens():
    """
    为一周7天随机安排4个饭堂用餐
    确保每个饭堂至少被安排一次
    返回：包含每天饭堂安排的列表
    """
    # 定义饭堂和星期
    canteens = ['第一饭堂', '第二饭堂', '第三饭堂', '第四饭堂']  # 4个饭堂
    weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']  # 7天
    
    # 初始化安排列表
    arrangement = []
    
    # 第一步：确保每个饭堂至少被安排一次
    # 从4个饭堂中随机选择4个（即所有饭堂），打乱顺序
    guaranteed_canteens = canteens.copy()  # 复制饭堂列表
    random.shuffle(guaranteed_canteens)    # 随机打乱顺序
    
    # 将前4天分配给4个不同的饭堂
    for i in range(4):
        arrangement.append(guaranteed_canteens[i])
    
    # 第二步：为剩余的3天随机分配饭堂
    for i in range(3):  # 还剩3天（周五、周六、周日）
        # 从4个饭堂中随机选择一个
        random_canteen = random.choice(canteens)
        arrangement.append(random_canteen)
    
    # 第三步：将整个安排再次随机打乱，避免前4天总是不同饭堂的模式
    random.shuffle(arrangement)
    
    return weekdays, arrangement

def verify_arrangement(arrangement):
    """
    验证安排是否满足每个饭堂至少被安排一次的要求
    参数：arrangement - 饭堂安排列表
    返回：True表示满足要求，False表示不满足
    """
    canteens = ['第一饭堂', '第二饭堂', '第三饭堂', '第四饭堂']
    
    # 检查每个饭堂是否都在安排中出现至少一次
    for canteen in canteens:
        if canteen not in arrangement:
            return False
    return True

def test_canteen_scheduler():
    print("=== 学校饭堂随机安排程序验证 ===")
    
    # 测试多次生成，验证算法的正确性
    test_count = 10
    success_count = 0
    
    print(f"进行 {test_count} 次随机安排测试：")
    print()
    
    for i in range(test_count):
        weekdays, arrangement = arrange_canteens()
        is_valid = verify_arrangement(arrangement)
        
        if is_valid:
            success_count += 1
        
        # 显示前3次的详细结果
        if i < 3:
            print(f"测试 {i+1}:")
            print(f"  安排：{arrangement}")
            
            # 统计每个饭堂出现次数
            canteen_count = {}
            for canteen in arrangement:
                canteen_count[canteen] = canteen_count.get(canteen, 0) + 1
            
            print(f"  统计：{canteen_count}")
            print(f"  验证：{'✅ 有效' if is_valid else '❌ 无效'}")
            print()
    
    print(f"测试结果：{success_count}/{test_count} 次成功")
    print(f"成功率：{success_count/test_count*100:.1f}%")
    
    # 详细展示一个完整的安排
    print("\\n=== 完整安排示例 ===")
    weekdays, canteen_arrangement = arrange_canteens()
    
    print(f"{'星期':<6} {'安排饭堂':<10}")
    print("-" * 20)
    
    for day, canteen in zip(weekdays, canteen_arrangement):
        print(f"{day:<6} {canteen:<10}")
    
    print("-" * 20)
    
    # 统计分析
    canteen_count = {}
    for canteen in canteen_arrangement:
        canteen_count[canteen] = canteen_count.get(canteen, 0) + 1
    
    print("\\n饭堂安排统计：")
    print(f"{'饭堂名称':<8} {'安排次数':<6}")
    print("-" * 18)
    
    for canteen in ['第一饭堂', '第二饭堂', '第三饭堂', '第四饭堂']:
        count = canteen_count.get(canteen, 0)
        print(f"{canteen:<8} {count:<6}")
    
    # 验证结果
    is_valid = verify_arrangement(canteen_arrangement)
    print(f"\\n验证结果：{'✅ 每个饭堂都至少安排一次' if is_valid else '❌ 存在未安排的饭堂'}")
    print(f"不同饭堂数：{len(set(canteen_arrangement))} 个")
    
    # 测试随机性
    print("\\n=== 随机性测试 ===")
    arrangements = []
    for i in range(5):
        _, arr = arrange_canteens()
        arrangements.append(tuple(arr))
    
    unique_arrangements = len(set(arrangements))
    print(f"生成5个方案，其中 {unique_arrangements} 个不同")
    print(f"随机性：{'✅ 良好' if unique_arrangements >= 3 else '⚠️ 一般'}")

if __name__ == "__main__":
    test_canteen_scheduler()
