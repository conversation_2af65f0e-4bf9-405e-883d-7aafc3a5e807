# 学校饭堂随机安排程序
# 使用random库为一周安排饭堂，确保每个饭堂至少被安排一次

import random

def arrange_canteens():
    """
    为一周7天随机安排4个饭堂用餐
    确保每个饭堂至少被安排一次
    返回：包含每天饭堂安排的列表
    """
    # 定义饭堂和星期
    canteens = ['第一饭堂', '第二饭堂', '第三饭堂', '第四饭堂']  # 4个饭堂
    weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']  # 7天
    
    # 初始化安排列表
    arrangement = []
    
    # 第一步：确保每个饭堂至少被安排一次
    # 从4个饭堂中随机选择4个（即所有饭堂），打乱顺序
    guaranteed_canteens = canteens.copy()  # 复制饭堂列表
    random.shuffle(guaranteed_canteens)    # 随机打乱顺序
    
    # 将前4天分配给4个不同的饭堂
    for i in range(4):
        arrangement.append(guaranteed_canteens[i])
    
    # 第二步：为剩余的3天随机分配饭堂
    for i in range(3):  # 还剩3天（周五、周六、周日）
        # 从4个饭堂中随机选择一个
        random_canteen = random.choice(canteens)
        arrangement.append(random_canteen)
    
    # 第三步：将整个安排再次随机打乱，避免前4天总是不同饭堂的模式
    random.shuffle(arrangement)
    
    return weekdays, arrangement

def verify_arrangement(arrangement):
    """
    验证安排是否满足每个饭堂至少被安排一次的要求
    参数：arrangement - 饭堂安排列表
    返回：True表示满足要求，False表示不满足
    """
    canteens = ['第一饭堂', '第二饭堂', '第三饭堂', '第四饭堂']
    
    # 检查每个饭堂是否都在安排中出现至少一次
    for canteen in canteens:
        if canteen not in arrangement:
            return False
    return True

print("=== 学校饭堂随机安排程序 ===")
print("学校共有4个饭堂，为一周7天随机安排用餐地点")
print("要求：每个饭堂至少被安排一次")
print("-" * 50)

# 生成饭堂安排
weekdays, canteen_arrangement = arrange_canteens()

# 验证安排是否满足要求
is_valid = verify_arrangement(canteen_arrangement)

# 输出安排结果
print("\n=== 本周饭堂安排 ===")
print(f"{'星期':<6} {'安排饭堂':<10}")
print("-" * 20)

for day, canteen in zip(weekdays, canteen_arrangement):
    print(f"{day:<6} {canteen:<10}")

print("-" * 20)

# 统计每个饭堂被安排的次数
canteen_count = {}
for canteen in canteen_arrangement:
    canteen_count[canteen] = canteen_count.get(canteen, 0) + 1

print("\n=== 饭堂安排统计 ===")
print(f"{'饭堂名称':<8} {'安排次数':<6}")
print("-" * 18)

for canteen in ['第一饭堂', '第二饭堂', '第三饭堂', '第四饭堂']:
    count = canteen_count.get(canteen, 0)
    print(f"{canteen:<8} {count:<6}")

# 验证结果
print(f"\n=== 验证结果 ===")
if is_valid:
    print("✅ 安排有效：每个饭堂都至少被安排一次")
else:
    print("❌ 安排无效：存在饭堂未被安排")

print(f"总安排天数：{len(canteen_arrangement)} 天")
print(f"不同饭堂数：{len(set(canteen_arrangement))} 个")

# 生成多个方案进行对比
print("\n=== 多方案对比 ===")
print("生成3个不同的随机安排方案：")
print()

for scheme in range(1, 4):
    days, arrangement = arrange_canteens()
    print(f"方案{scheme}：", end="")
    for day, canteen in zip(days, arrangement):
        print(f"{day}({canteen[-3:]}) ", end="")
    
    # 验证方案
    valid = verify_arrangement(arrangement)
    status = "✅" if valid else "❌"
    print(f" {status}")

print("\n程序执行完成！每次运行都会生成不同的随机安排。")