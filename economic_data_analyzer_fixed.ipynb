# 全球主要国家经济数据分析程序（修复版）
# 包含完整的错误处理和诊断功能

import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import matplotlib
import os

# 设置中文字体支持，解决中文乱码问题
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False

print("=== 全球主要国家经济数据分析（修复版） ===")
print("分析GDP、人口、失业率等经济指标")
print("-" * 50)

# 检查必要的库
try:
    import openpyxl
    print("✅ openpyxl库已安装")
except ImportError:
    print("❌ 缺少openpyxl库，请运行: pip install openpyxl")
    raise

# (1) 数据读取与初步检查
print("\n=== (1) 数据读取与初步检查 ===")

# 尝试多个可能的文件路径
possible_paths = [
    r'C:\Users\<USER>\Desktop\fenix\mvsData.xlsx',
    'mvsData.xlsx',  # 当前目录
    r'C:\Users\<USER>\Desktop\mvsData.xlsx',
    r'Desktop\fenix\mvsData.xlsx'
]

file_path = None
for path in possible_paths:
    if os.path.exists(path):
        file_path = path
        print(f"✅ 找到文件: {file_path}")
        break
    else:
        print(f"❌ 文件不存在: {path}")

if file_path is None:
    print("\n❌ 所有路径都找不到文件，请检查文件位置")
    print("当前工作目录:", os.getcwd())
    print("请将mvsData.xlsx文件放在当前目录，或修改文件路径")
    raise FileNotFoundError("找不到mvsData.xlsx文件")

# 读取Excel文件
try:
    # 先检查工作表
    xl_file = pd.ExcelFile(file_path, engine='openpyxl')
    print(f"可用工作表: {xl_file.sheet_names}")
    
    # 尝试读取d1工作表
    if 'd1' in xl_file.sheet_names:
        df = pd.read_excel(file_path, sheet_name='d1', engine='openpyxl')
        print("✅ 成功读取d1工作表")
    else:
        print("❌ 没有找到d1工作表，使用第一个工作表")
        df = pd.read_excel(file_path, sheet_name=0, engine='openpyxl')
        print(f"✅ 成功读取工作表: {xl_file.sheet_names[0]}")
        
except Exception as e:
    print(f"❌ 读取Excel文件失败: {e}")
    raise

# 检查数据结构
print(f"\n数据形状: {df.shape}")
print(f"列名: {list(df.columns)}")

# 检查必要的列是否存在
required_columns = ['国家', '国内生产总值（十亿美元）', '人口（百万）', '失业率（%）']
missing_columns = [col for col in required_columns if col not in df.columns]

if missing_columns:
    print(f"❌ 缺少必要的列: {missing_columns}")
    print("请检查Excel文件的列名是否正确")
    print("实际列名:", list(df.columns))
    raise ValueError(f"缺少必要的列: {missing_columns}")
else:
    print("✅ 所有必要的列都存在")

# ② 重置索引为1~20
df.index = range(1, len(df) + 1)
print(f"✅ 索引已重置为1~{len(df)}")

# ③ 显示前21行数据（验证是否包含全部数据）
print("\n前21行数据（实际数据行数）：")
print(df.head(21))

# (2) 数据选择与提取
print("\n=== (2) 数据选择与提取 ===")

# ① 单列提取：提取GDP列
gdp_data = df['国内生产总值（十亿美元）']
print("\n① 所有国家的GDP数据：")
print(gdp_data)

# ② 多列提取：提取国家和失业率
country_unemployment = df[['国家', '失业率（%）']]
print("\n② 国家和失业率数据：")
print(country_unemployment)

# (3) 条件筛选
print("\n=== (3) 条件筛选 ===")

# 筛选失业率低于4%的国家
low_unemployment = df[df['失业率（%）'] < 4][['国家', '失业率（%）']]
print("\n失业率低于4%的国家：")
print(low_unemployment)

# (4) 计算新列
print("\n=== (4) 计算新列 ===")

# ① 计算人均GDP：GDP(十亿美元) × 10^9 / 人口(百万) × 10^6
df['人均GDP（美元）'] = (df['国内生产总值（十亿美元）'] * 1e9) / (df['人口（百万）'] * 1e6)

# ② 输出前5名国家的人均GDP
top5_per_capita = df.nlargest(5, '人均GDP（美元）')[['国家', '人均GDP（美元）']]
print("\n人均GDP前5名国家：")
print(top5_per_capita)

# (5) 数据排序
print("\n=== (5) 数据排序 ===")

# 按GDP从高到低排序，输出前5名
top5_gdp = df.nlargest(5, '国内生产总值（十亿美元）')[['国家', '国内生产总值（十亿美元）']]
print("\nGDP前5名国家：")
print(top5_gdp)

# (6) 数据质量检查
print("\n=== (6) 数据质量检查 ===")

# ① 统计缺失值
missing_values = df.isnull().sum()
print("\n① 各列缺失值数量：")
print(missing_values)

# ② 统计重复值
duplicate_count = df.duplicated().sum()
print(f"\n② 重复值数量：{duplicate_count}")

# (7) 统计分析
print("\n=== (7) 统计分析 ===")

# ① 描述性统计
numeric_columns = ['国内生产总值（十亿美元）', '人口（百万）', '失业率（%）', '人均GDP（美元）']
desc_stats = df[numeric_columns].describe()
print("\n① 数值列描述性统计：")
print(desc_stats)

# ② 相关系数矩阵（映射为英文列名）
df_corr = df[numeric_columns].copy()
df_corr.columns = ['GDP', 'Population', 'Unemployment', 'GDP_per_capita']
correlation_matrix = df_corr.corr()
print("\n② 相关系数矩阵：")
print(correlation_matrix)

# (8) 数据可视化
print("\n=== (8) 数据可视化 ===")
print("正在生成图表...")

try:
    # 创建2行2列的子图，画布大小15x10英寸
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
    
    # 子图1：GDP前10国家条形图
    top10_gdp = df.nlargest(10, '国内生产总值（十亿美元）')
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', 
              '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9']
    ax1.bar(top10_gdp['国家'], top10_gdp['国内生产总值（十亿美元）'], 
            color=colors[:len(top10_gdp)])
    ax1.set_title('GDP Top10国家', fontsize=14, fontweight='bold')
    ax1.set_ylabel('GDP（十亿美元）', fontsize=12)
    ax1.tick_params(axis='x', rotation=45)
    ax1.grid(True, alpha=0.3)
    
    # 子图2：各国失业率分布折线图
    ax2.plot(df['国家'], df['失业率（%）'], linestyle='--', marker='o', 
             linewidth=2, markersize=6, color='#E74C3C')
    ax2.set_title('各国失业率分布', fontsize=14, fontweight='bold')
    ax2.set_ylabel('失业率（%）', fontsize=12)
    ax2.tick_params(axis='x', rotation=90)
    ax2.grid(True, alpha=0.3)
    
    # 子图3：GDP与失业率关系散点图
    ax3.scatter(df['国内生产总值（十亿美元）'], df['失业率（%）'], 
               s=100, alpha=0.7, c='#3498DB', edgecolors='black')
    ax3.set_title('GDP与失业率关系', fontsize=14, fontweight='bold')
    ax3.set_xlabel('GDP（十亿美元）', fontsize=12)
    ax3.set_ylabel('失业率（%）', fontsize=12)
    ax3.grid(True, alpha=0.3)
    
    # 子图4：人均GDP前10国家
    top10_per_capita = df.nlargest(10, '人均GDP（美元）')
    ax4.barh(top10_per_capita['国家'], top10_per_capita['人均GDP（美元）'], 
             color='#2ECC71')
    ax4.set_title('人均GDP Top10国家', fontsize=14, fontweight='bold')
    ax4.set_xlabel('人均GDP（美元）', fontsize=12)
    ax4.grid(True, alpha=0.3)
    
    # 自动调整子图间距
    plt.tight_layout()
    plt.show()
    
    print("✅ 图表生成完成")
    
except Exception as e:
    print(f"❌ 图表生成失败: {e}")
    print("可能是中文字体问题，尝试使用默认字体")

# 输出最终汇总报告
print("\n=== 分析汇总报告 ===")
print(f"数据集包含 {len(df)} 个国家的经济数据")
print(f"GDP最高国家：{df.loc[df['国内生产总值（十亿美元）'].idxmax(), '国家']}")
print(f"人均GDP最高国家：{df.loc[df['人均GDP（美元）'].idxmax(), '国家']}")
print(f"失业率最低国家：{df.loc[df['失业率（%）'].idxmin(), '国家']}")
print(f"失业率最高国家：{df.loc[df['失业率（%）'].idxmax(), '国家']}")
print(f"平均GDP：{df['国内生产总值（十亿美元）'].mean():.2f} 十亿美元")
print(f"平均失业率：{df['失业率（%）'].mean():.2f}%")

print("\n程序执行完成！")