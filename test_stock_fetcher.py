# 测试股票数据获取程序（模拟版本）
import pandas as pd
import datetime
import random
import numpy as np

def test_stock_data_fetcher():
    print("=== 股票数据获取程序测试版 ===")
    print("模拟Baostock数据获取流程")
    print("-" * 50)

    # 模拟登录
    print("正在模拟登录Baostock系统...")
    print("✅ 模拟登录成功")

    try:
        # 设置股票信息
        stock_code = "sz.000001"
        stock_name = "平安银行"

        # 生成日期范围
        end_date = datetime.date(2024, 1, 19)
        start_date = end_date - datetime.timedelta(days=15)

        start_date_str = start_date.strftime('%Y-%m-%d')
        end_date_str = end_date.strftime('%Y-%m-%d')

        print(f"\\n股票代码：{stock_code}（{stock_name}）")
        print(f"查询日期范围：{start_date_str} 至 {end_date_str}")

        # 模拟生成股票数据
        print("\\n正在模拟生成历史K线数据...")

        # 生成10个交易日的模拟数据
        dates = []
        current_date = start_date
        while len(dates) < 10 and current_date <= end_date:
            # 跳过周末
            if current_date.weekday() < 5:  # 0-4是周一到周五
                dates.append(current_date.strftime('%Y-%m-%d'))
            current_date += datetime.timedelta(days=1)

        # 生成模拟股价数据
        base_price = 10.0  # 基础价格
        data_list = []

        for i, date in enumerate(dates):
            # 模拟价格波动
            price_change = random.uniform(-0.5, 0.5)
            open_price = base_price + price_change
            high_price = open_price + random.uniform(0, 0.3)
            low_price = open_price - random.uniform(0, 0.3)
            close_price = open_price + random.uniform(-0.2, 0.2)

            # 计算涨跌幅
            if i == 0:
                pct_chg = 0.0
                preclose = open_price
            else:
                preclose = float(data_list[i-1][6])  # 前一天收盘价，转换为float
                pct_chg = ((close_price - preclose) / preclose) * 100

            # 生成成交量和成交额
            volume = random.randint(1000000, 10000000)
            amount = volume * close_price
            turn = random.uniform(0.1, 2.0)

            row_data = [
                date,                    # date
                stock_code,              # code
                f"{open_price:.2f}",     # open
                f"{high_price:.2f}",     # high
                f"{low_price:.2f}",      # low
                f"{close_price:.2f}",    # close
                f"{preclose:.2f}",       # preclose
                str(volume),             # volume
                f"{amount:.2f}",         # amount
                "3",                     # adjustflag
                f"{turn:.2f}",           # turn
                "1",                     # tradestatus
                f"{pct_chg:.2f}",        # pctChg
                "0"                      # isST
            ]
            data_list.append(row_data)
            base_price = close_price  # 更新基础价格

        print("✅ 模拟数据生成成功")

        # 转换为DataFrame
        print("\\n正在处理数据...")

        columns = ['date', 'code', 'open', 'high', 'low', 'close', 'preclose',
                  'volume', 'amount', 'adjustflag', 'turn', 'tradestatus', 'pctChg', 'isST']

        result = pd.DataFrame(data_list, columns=columns)

        print(f"✅ 成功生成 {len(result)} 条数据")

        # 数据类型转换
        numeric_columns = ['open', 'high', 'low', 'close', 'preclose', 'volume', 'amount', 'turn', 'pctChg']
        for col in numeric_columns:
            result[col] = pd.to_numeric(result[col], errors='coerce')

        # 添加中文列名
        result_cn = result.copy()
        column_mapping = {
            'date': '日期',
            'code': '股票代码',
            'open': '开盘价',
            'high': '最高价',
            'low': '最低价',
            'close': '收盘价',
            'preclose': '前收盘价',
            'volume': '成交量',
            'amount': '成交额',
            'turn': '换手率',
            'pctChg': '涨跌幅',
            'tradestatus': '交易状态',
            'isST': '是否ST'
        }

        result_cn.rename(columns=column_mapping, inplace=True)

        # 显示数据预览
        print("\\n数据预览（前5行）：")
        print(result_cn[['日期', '开盘价', '最高价', '最低价', '收盘价', '成交量', '涨跌幅']].head())

        # 保存到Excel文件
        print("\\n正在保存数据到文件...")

        filename = f"{stock_name}_{stock_code.replace('.', '_')}_{start_date_str}_to_{end_date_str}_test.xlsx"

        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            # 保存原始数据
            result.to_excel(writer, sheet_name='原始数据', index=False)

            # 保存中文数据
            result_cn.to_excel(writer, sheet_name='中文数据', index=False)

            # 创建数据统计表
            stats_data = {
                '统计项目': ['数据条数', '日期范围', '最高价', '最低价', '平均收盘价', '总成交量', '平均涨跌幅'],
                '数值': [
                    len(result),
                    f"{result['date'].min()} 至 {result['date'].max()}",
                    f"{result['high'].max():.2f}",
                    f"{result['low'].min():.2f}",
                    f"{result['close'].mean():.2f}",
                    f"{result['volume'].sum():,.0f}",
                    f"{result['pctChg'].mean():.2f}%"
                ]
            }
            stats_df = pd.DataFrame(stats_data)
            stats_df.to_excel(writer, sheet_name='数据统计', index=False)

        print(f"✅ 测试数据已成功保存到文件：{filename}")
        print(f"文件包含3个工作表：原始数据、中文数据、数据统计")

        # 显示统计信息
        print("\\n=== 数据统计信息 ===")
        print(f"股票名称：{stock_name}")
        print(f"股票代码：{stock_code}")
        print(f"数据条数：{len(result)} 条")
        print(f"日期范围：{result['date'].min()} 至 {result['date'].max()}")
        print(f"最高价：{result['high'].max():.2f} 元")
        print(f"最低价：{result['low'].min():.2f} 元")
        print(f"平均收盘价：{result['close'].mean():.2f} 元")
        print(f"总成交量：{result['volume'].sum():,.0f} 股")
        print(f"平均涨跌幅：{result['pctChg'].mean():.2f}%")

    except Exception as e:
        print(f"❌ 程序执行出错：{str(e)}")

    finally:
        # 模拟退出
        print("\\n正在模拟退出Baostock系统...")
        print("✅ 已安全退出Baostock系统")
        print("\\n测试程序执行完成！")

if __name__ == "__main__":
    test_stock_data_fetcher()
