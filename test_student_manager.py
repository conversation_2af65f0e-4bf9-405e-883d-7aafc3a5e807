# 验证学生成绩管理系统
def test_student_manager():
    # 创建测试字典（简化版）
    economics_trade = {
        "202210137004": {"姓名": "罗锦昊", "成绩": 78},
        "202210137012": {"姓名": "杨阳", "成绩": 94},
        "202210137019": {"姓名": "林华鑫", "成绩": 95},
        "202210137040": {"姓名": "莫诗雅", "成绩": 75},
        "202310137067": {"姓名": "张某某", "成绩": 88},
        "202310137068": {"姓名": "李某某", "成绩": 92},
    }
    
    print("=== 学生成绩管理系统验证 ===")
    print("初始数据：")
    for sid, info in economics_trade.items():
        print(f"{sid}: {info}")
    
    # (1) 修改张某某的成绩状态为"缺考"
    economics_trade["202310137067"]["成绩"] = "缺考"
    print("\\n(1) 修改张某某成绩为缺考 ✅")
    
    # (2) 将本人成绩修改为100
    economics_trade["202210137004"]["姓名"] = "张家铭"
    economics_trade["202210137004"]["成绩"] = 100
    print("(2) 修改张家铭成绩为100 ✅")
    
    # (3) 删除李某某
    del economics_trade["202310137068"]
    print("(3) 删除李某某信息 ✅")
    
    # (4) 遍历输出
    print("\\n(4) 当前学生信息：")
    for student_id, info in economics_trade.items():
        print(f"{student_id}: {info['姓名']} - {info['成绩']}")
    
    # (5) 统计总人数
    total_students = len(economics_trade)
    print(f"\\n(5) 总人数：{total_students} 人 ✅")
    
    # (6) 模拟查询
    print("\\n(6) 查询测试：")
    test_ids = ["202210137004", "999999999"]
    for test_id in test_ids:
        if test_id in economics_trade:
            info = economics_trade[test_id]
            print(f"找到：{test_id} - {info['姓名']} - {info['成绩']}")
        else:
            print(f"没找到该同学：{test_id}")
    
    # (7) 统计80分以上
    high_score_count = 0
    for student_id, info in economics_trade.items():
        if isinstance(info['成绩'], (int, float)) and info['成绩'] >= 80:
            high_score_count += 1
    
    print(f"\\n(7) 80分以上学生：{high_score_count} 名 ✅")
    
    print("\\n所有功能验证完成！")

if __name__ == "__main__":
    test_student_manager()
