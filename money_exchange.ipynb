# 钞票兑换方案计算器
# 100元旧钞兑换为100、50、20、10元新钞的所有方案

print("=== 100元钞票兑换方案 ===")
print("可兑换面值：100元、50元、20元、10元")
print("方案编号 100元 50元 20元 10元")
print("-" * 30)

total_money = 100  # 总金额100元
scheme_count = 0   # 方案计数器

# 使用四重for循环遍历所有可能的组合
for count_100 in range(total_money // 100 + 1):  # 100元张数：0-1张
    for count_50 in range((total_money - count_100 * 100) // 50 + 1):  # 50元张数
        for count_20 in range((total_money - count_100 * 100 - count_50 * 50) // 20 + 1):  # 20元张数
            # 计算剩余金额
            remaining = total_money - count_100 * 100 - count_50 * 50 - count_20 * 20
            
            # 使用if语句判断剩余金额是否能被10整除
            if remaining >= 0 and remaining % 10 == 0:
                count_10 = remaining // 10  # 计算10元张数
                scheme_count += 1  # 方案数量加1
                
                # 输出兑换方案
                print(f"方案{scheme_count:>3}   {count_100:>3}   {count_50:>2}   {count_20:>2}   {count_10:>2}")

print("-" * 30)
print(f"总共有 {scheme_count} 种兑换方案")