{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Looking in indexes: https://pypi.mirrors.ustc.edu.cn/simple\n", "Requirement already satisfied: baostock in ./venv/lib/python3.12/site-packages (0.8.9)\n", "Requirement already satisfied: pandas>=0.18.0 in ./venv/lib/python3.12/site-packages (from baostock) (2.2.3)\n", "Requirement already satisfied: numpy>=1.26.0 in ./venv/lib/python3.12/site-packages (from pandas>=0.18.0->baostock) (2.2.3)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in ./venv/lib/python3.12/site-packages (from pandas>=0.18.0->baostock) (2.9.0.post0)\n", "Requirement already satisfied: pytz>=2020.1 in ./venv/lib/python3.12/site-packages (from pandas>=0.18.0->baostock) (2025.1)\n", "Requirement already satisfied: tzdata>=2022.7 in ./venv/lib/python3.12/site-packages (from pandas>=0.18.0->baostock) (2025.1)\n", "Requirement already satisfied: six>=1.5 in ./venv/lib/python3.12/site-packages (from python-dateutil>=2.8.2->pandas>=0.18.0->baostock) (1.17.0)\n", "\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m A new release of pip is available: \u001b[0m\u001b[31;49m24.0\u001b[0m\u001b[39;49m -> \u001b[0m\u001b[32;49m25.0.1\u001b[0m\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m To update, run: \u001b[0m\u001b[32;49mpip install --upgrade pip\u001b[0m\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["www.marscode.cn "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["login success!\n", "login respond error_code:0\n", "login respond  error_msg:success\n", "query_history_k_data_plus respond error_code:0\n", "query_history_k_data_plus respond  error_msg:success\n", "           date       code     open     high      low    close preclose  \\\n", "0    2024-07-01  sh.600000   8.2200   8.3000   8.2000   8.2800   8.2300   \n", "1    2024-07-02  sh.600000   8.2700   8.5400   8.2700   8.5300   8.2800   \n", "2    2024-07-03  sh.600000   8.5300   8.6000   8.4800   8.5300   8.5300   \n", "3    2024-07-04  sh.600000   8.5900   8.6300   8.4800   8.5000   8.5300   \n", "4    2024-07-05  sh.600000   8.5400   8.5500   8.2900   8.3400   8.5000   \n", "..          ...        ...      ...      ...      ...      ...      ...   \n", "120  2024-12-25  sh.600000  10.1300  10.3900  10.1300  10.3500  10.1300   \n", "121  2024-12-26  sh.600000  10.3500  10.3900  10.1600  10.3400  10.3500   \n", "122  2024-12-27  sh.600000  10.3300  10.3900  10.1200  10.3600  10.3400   \n", "123  2024-12-30  sh.600000  10.3400  10.4900  10.3100  10.4700  10.3600   \n", "124  2024-12-31  sh.600000  10.4300  10.5600  10.2800  10.2900  10.4700   \n", "\n", "       volume          amount adjustflag      turn tradestatus     pctChg isST  \n", "0    28732962  237294502.0600          3  0.097900           1   0.607500    0  \n", "1    57345810  485897423.6600          3  0.195400           1   3.019300    0  \n", "2    46409668  396512668.3500          3  0.158100           1   0.000000    0  \n", "3    44412066  379773335.1000          3  0.151300           1  -0.351700    0  \n", "4    51997018  436429580.8400          3  0.177100           1  -1.882400    0  \n", "..        ...             ...        ...       ...         ...        ...  ...  \n", "120  72739600  749135068.9400          3  0.247800           1   2.171800    0  \n", "121  61152236  628394286.5800          3  0.208300           1  -0.096600    0  \n", "122  69506374  714702133.5700          3  0.236800           1   0.193400    0  \n", "123  78477805  818657791.7100          3  0.267400           1   1.061800    0  \n", "124  57783443  602393523.1200          3  0.196900           1  -1.719200    0  \n", "\n", "[125 rows x 14 columns]\n", "logout success!\n"]}, {"data": {"text/plain": ["<baostock.data.resultset.ResultData at 0x7f4b52708b60>"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import baostock as bs\n", "import pandas as pd\n", "\n", "#### 登陆系统 ####\n", "lg = bs.login()\n", "# 显示登陆返回信息\n", "print('login respond error_code:'+lg.error_code)\n", "print('login respond  error_msg:'+lg.error_msg)\n", "\n", "#### 获取沪深A股历史K线数据 ####\n", "# 详细指标参数，参见“历史行情指标参数”章节；“分钟线”参数与“日线”参数不同。“分钟线”不包含指数。\n", "# 分钟线指标：date,time,code,open,high,low,close,volume,amount,adjustflag\n", "# 周月线指标：date,code,open,high,low,close,volume,amount,adjustflag,turn,pctChg\n", "rs = bs.query_history_k_data_plus(\"sh.600000\",\n", "    \"date,code,open,high,low,close,preclose,volume,amount,adjustflag,turn,tradestatus,pctChg,isST\",\n", "    start_date='2024-07-01', end_date='2024-12-31',\n", "    frequency=\"d\", adjustflag=\"3\")\n", "print('query_history_k_data_plus respond error_code:'+rs.error_code)\n", "print('query_history_k_data_plus respond  error_msg:'+rs.error_msg)\n", "\n", "#### 打印结果集 ####\n", "data_list = []\n", "while (rs.error_code == '0') & rs.next():\n", "    # 获取一条记录，将记录合并在一起\n", "    data_list.append(rs.get_row_data())\n", "result = pd.DataFrame(data_list, columns=rs.fields)\n", "\n", "#### 结果集输出到csv文件 ####   \n", "result.to_csv(\"D\\\\history_A_stock_k_data.csv\", index=False)\n", "print(result)\n", "\n", "#### 登出系统 ####\n", "bs.logout()\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["login success!\n", "login respond error_code:0\n", "login respond  error_msg:success\n", "query_history_k_data_plus respond error_code:0\n", "query_history_k_data_plus respond  error_msg:success\n", "         date       code      close      peTTM     pbMRQ      psTTM  \\\n", "0  2025-02-17  sh.600519  1471.6200  22.357583  7.776913  10.979860   \n", "1  2025-02-18  sh.600519  1475.0000  22.408933  7.794775  11.005079   \n", "2  2025-02-19  sh.600519  1491.0000  22.652013  7.879329  11.124456   \n", "3  2025-02-20  sh.600519  1474.0000  22.393741  7.789491  10.997617   \n", "4  2025-02-21  sh.600519  1488.2100  22.609626  7.864585  11.103639   \n", "5  2025-02-24  sh.600519  1479.0700  22.470767  7.816283  11.035445   \n", "6  2025-02-25  sh.600519  1454.0000  22.089891  7.683799  10.848396   \n", "\n", "     pcfNcfTTM  \n", "0  -445.088472  \n", "1  -446.110746  \n", "2  -450.949913  \n", "3  -445.808298  \n", "4  -450.106083  \n", "5  -447.341709  \n", "6  -439.759338  \n", "logout success!\n"]}, {"data": {"text/plain": ["<baostock.data.resultset.ResultData at 0x7f4b508397f0>"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["import baostock as bs\n", "import pandas as pd\n", "\n", "#### 登陆系统 ####\n", "lg = bs.login()\n", "# 显示登陆返回信息\n", "print('login respond error_code:'+lg.error_code)\n", "print('login respond  error_msg:'+lg.error_msg)\n", "\n", "#### 获取沪深A股估值指标(日频)数据 ####\n", "# peTTM    滚动市盈率\n", "# psTTM    滚动市销率\n", "# pcfNcfTTM    滚动市现率\n", "# pbMRQ    市净率\n", "rs = bs.query_history_k_data_plus(\"sh.600000\",\n", "    \"date,code,close,peTTM,pbMRQ,psTTM,pcfNcfTTM\",\n", "    start_date='2015-01-01', end_date='2017-12-31', \n", "    frequency=\"d\", adjustflag=\"3\")\n", "print('query_history_k_data_plus respond error_code:'+rs.error_code)\n", "print('query_history_k_data_plus respond  error_msg:'+rs.error_msg)\n", "\n", "#### 打印结果集 ####\n", "result_list = []\n", "while (rs.error_code == '0') & rs.next():\n", "    # 获取一条记录，将记录合并在一起\n", "    result_list.append(rs.get_row_data())\n", "result = pd.DataFrame(result_list, columns=rs.fields)\n", "\n", "#### 结果集输出到csv文件 ####\n", "result.to_csv(\"D:\\\\history_A_stock_valuation_indicator_data.csv\", encoding=\"gbk\", index=False)\n", "print(result)\n", "\n", "#### 登出系统 ####\n", "bs.logout()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["login success!\n", "login respond error_code:0\n", "login respond  error_msg:success\n", "query_history_k_data_plus respond error_code:0\n", "query_history_k_data_plus respond  error_msg:success\n", "         date       code    close     peTTM     pbMRQ     psTTM pcfNcfTTM\n", "0  2025-02-14  sh.600000  10.4100  6.751578  0.477427  1.789515  1.253117\n", "1  2025-02-17  sh.600000  10.2700  6.660779  0.471006  1.765449  1.236264\n", "2  2025-02-18  sh.600000  10.5000  6.809949  0.481555  1.804987  1.263950\n", "3  2025-02-19  sh.600000  10.4400  6.771035  0.478803  1.794673  1.256728\n", "4  2025-02-20  sh.600000  10.3900  6.738607  0.476510  1.786077  1.250709\n", "5  2025-02-21  sh.600000  10.2900  6.673750  0.471924  1.768887  1.238671\n", "6  2025-02-24  sh.600000  10.2100  6.621865  0.468255  1.755135  1.229041\n", "7  2025-02-25  sh.600000  10.1000  6.550523  0.463210  1.736225  1.215800\n", "logout success!\n"]}, {"data": {"text/plain": ["<baostock.data.resultset.ResultData at 0x7f4b5083be60>"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["import baostock as bs\n", "import pandas as pd\n", "\n", "#### 登陆系统 ####\n", "lg = bs.login()\n", "# 显示登陆返回信息\n", "print('login respond error_code:'+lg.error_code)\n", "print('login respond  error_msg:'+lg.error_msg)\n", "\n", "#### 获取沪深A股估值指标(日频)数据 ####\n", "# peTTM    滚动市盈率\n", "# psTTM    滚动市销率\n", "# pcfNcfTTM    滚动市现率\n", "# pbMRQ    市净率\n", "rs = bs.query_history_k_data_plus(\"sh.600000\",\n", "    \"date,code,close,peTTM,pbMRQ,psTTM,pcfNcfTTM\",\n", "    start_date='2025-02-14', end_date='2025-02-25', \n", "    frequency=\"d\", adjustflag=\"3\")\n", "print('query_history_k_data_plus respond error_code:'+rs.error_code)\n", "print('query_history_k_data_plus respond  error_msg:'+rs.error_msg)\n", "\n", "#### 打印结果集 ####\n", "result_list = []\n", "while (rs.error_code == '0') & rs.next():\n", "    # 获取一条记录，将记录合并在一起\n", "    result_list.append(rs.get_row_data())\n", "result = pd.DataFrame(result_list, columns=rs.fields)\n", "\n", "#### 结果集输出到csv文件 ####\n", "result.to_csv(\"D:\\\\456.csv\", encoding=\"gbk\", index=False)\n", "print(result)\n", "\n", "#### 登出系统 ####\n", "bs.logout()"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 2}