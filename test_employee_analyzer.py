# 验证员工性别统计分析器
def is_female(id_number):
    """
    判断身份证号码代表的性别是否为女性
    参数：id_number - 18位身份证号码字符串
    返回：True表示女性，False表示男性
    """
    # 检查身份证号码长度是否为18位
    if len(id_number) != 18:
        return False

    # 获取倒数第二位数字（性别位）
    gender_digit = int(id_number[-2])

    # 偶数代表女性，奇数代表男性
    return gender_digit % 2 == 0

def test_employee_analyzer():
    print("=== 员工性别统计分析器验证 ===")

    # 创建示例员工数据文件
    employees_data = """员工ID,姓名,职位,部门,联系方式,身份证号码
001,张三,软件工程师,技术部,13800138001,110101199001011234
002,李四,产品经理,产品部,13800138002,110102199102022345
003,王五,UI设计师,设计部,13800138003,110103199203033456
004,赵六,数据分析师,技术部,13800138004,110104199304044567
005,钱七,市场专员,市场部,13800138005,110105199405055678
006,孙八,人事专员,人事部,13800138006,110106199506066789
007,周九,财务专员,财务部,13800138007,110107199607077890
008,吴十,运营专员,运营部,13800138008,110108199708088901"""

    # 写入员工数据到文件
    with open('employees.txt', 'w', encoding='utf-8') as file:
        file.write(employees_data)

    print("已创建示例员工数据文件 employees.txt")

    # 测试身份证性别判断函数
    print("\\n身份证性别判断测试：")
    test_ids = [
        ("110101199001011234", "张三", "男性"),  # 倒数第二位是3（奇数）
        ("110102199102022345", "李四", "男性"),  # 倒数第二位是4（偶数）应该是女性
        ("110103199203033456", "王五", "女性"),  # 倒数第二位是5（奇数）应该是男性
        ("110104199304044567", "赵六", "女性"),  # 倒数第二位是6（偶数）
    ]

    print(f"{'姓名':<6} {'身份证号码':<20} {'预期性别':<6} {'实际判断':<6} {'结果':<4}")
    print("-" * 50)

    for id_num, name, expected in test_ids:
        actual = "女性" if is_female(id_num) else "男性"
        result = "✅" if actual == expected else "❌"
        print(f"{name:<6} {id_num:<20} {expected:<6} {actual:<6} {result:<4}")

    # 读取文件并统计
    print("\\n文件处理和统计：")
    try:
        with open('employees.txt', 'r', encoding='utf-8') as file:
            lines = file.readlines()

        # 跳过第一行标题行
        employee_lines = lines[1:]

        print(f"读取到 {len(employee_lines)} 条员工记录")

        # 初始化女性员工计数器
        female_count = 0

        print(f"\\n{'姓名':<8} {'身份证号码':<20} {'倒数第二位':<8} {'性别':<6}")
        print("-" * 50)

        # 对每一行数据进行处理
        for line in employee_lines:
            # 去除换行符并按逗号分割
            fields = line.strip().split(',')

            # 提取员工信息
            name = fields[1]         # 姓名
            id_number = fields[5]    # 身份证号码

            # 获取倒数第二位数字
            gender_digit = id_number[-2]

            # 调用函数判断该员工是否为女性
            if is_female(id_number):
                gender = "女性"
                female_count += 1
            else:
                gender = "男性"

            # 输出员工性别信息
            print(f"{name:<8} {id_number:<20} {gender_digit:<8} {gender:<6}")

        print("-" * 50)
        print(f"\\n=== 统计结果 ===")
        print(f"总员工数：{len(employee_lines)} 人")
        print(f"女性员工数：{female_count} 人")
        print(f"男性员工数：{len(employee_lines) - female_count} 人")
        print(f"女性员工比例：{female_count / len(employee_lines) * 100:.1f}%")

    except FileNotFoundError:
        print("❌ 错误：找不到 employees.txt 文件")
    except Exception as e:
        print(f"❌ 处理文件时发生错误：{e}")

if __name__ == "__main__":
    test_employee_analyzer()
