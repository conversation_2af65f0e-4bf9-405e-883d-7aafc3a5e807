
print("=== 复利现值计算器 ===")
print("计算条件：年回报率 3%，存入年限 5 年")
future_value = float(input("请输入复利终值（元）："))
annual_balances = []  # 存储每年本息余额的列表
INTEREST_RATE = 0.03  # 年回报率常量（3%）
YEARS = 5  # 存入年限常量（5年）
print(f"\n开始计算复利现值...")
print(f"复利终值：{future_value:.2f} 元")
print(f"年回报率：{INTEREST_RATE * 100}%")
print(f"存入年限：{YEARS} 年")
current_balance = future_value  # 当前余额，从终值开始
annual_balances.append(current_balance)
for year in range(YEARS - 1, 0, -1):  # 从第4年到第1年
    previous_balance = current_balance / (1 + INTEREST_RATE)
    annual_balances.append(previous_balance)
    current_balance = previous_balance
    print(f"第{year}年末本息余额：{previous_balance:.2f} 元")
principal = current_balance / (1 + INTEREST_RATE)
annual_balances.append(principal)
print(f"初始投入本金：{principal:.2f} 元")
print()
annual_balances.reverse()
print("列表已反转，按时间顺序排列")
print("=== 复利现值计算结果 ===")
print("投入本金：{:.2f} 元".format(annual_balances[0]))
print("各年本息余额明细：")
for i in range(len(annual_balances)):
    if i == 0:
        print(f"第 {i} 年初（投入本金）：{annual_balances[i]:.2f} 元")
    else:
        print(f"第 {i} 年末（本息余额）：{annual_balances[i]:.2f} 元")
print("各年利息收入明细：")
for i in range(1, len(annual_balances)):
    interest_earned = annual_balances[i] - annual_balances[i-1]
    print(f"第 {i} 年利息收入：{interest_earned:.2f} 元")
print("计算完成！")