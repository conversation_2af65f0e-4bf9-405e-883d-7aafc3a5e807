# 销售数据分析程序
# 使用Numpy和Matplotlib进行销售数据的统计分析和可视化

import numpy as np
import matplotlib.pyplot as plt
import matplotlib

# 设置中文字体支持
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False

print("=== 销售数据分析程序 ===")
print("分析5天5种产品的销售数据")
print("-" * 40)

# (1) 数据准备：创建5x5随机整数矩阵
# 设置随机种子保证数据可复现
np.random.seed(0)

# 创建5x5矩阵，元素值在10到100之间
sales_data = np.random.randint(10, 101, size=(5, 5))

# 定义产品名称和日期
products = ['产品A', '产品B', '产品C', '产品D', '产品E']
days = ['第1天', '第2天', '第3天', '第4天', '第5天']

print("\n(1) 销售数据矩阵（5天×5种产品）：")
print(f"{'天数':<6}", end="")
for product in products:
    print(f"{product:<8}", end="")
print()
print("-" * 50)

for i, day in enumerate(days):
    print(f"{day:<6}", end="")
    for j in range(5):
        print(f"{sales_data[i, j]:<8}", end="")
    print()

# (2) 销售总额计算
total_sales = np.sum(sales_data)
print(f"\n(2) 5天总销售额：{total_sales} 件")

# (3) 日均销售额：计算每种产品的日均销售量
daily_avg_sales = np.mean(sales_data, axis=0)  # 按列计算平均值

print("\n(3) 各产品日均销售量：")
for i, product in enumerate(products):
    print(f"{product}：{daily_avg_sales[i]:.2f} 件/天")

# (4) 找出畅销产品
best_product_index = np.argmax(daily_avg_sales)  # 找到最大值的索引
best_product_name = products[best_product_index]
best_product_sales = daily_avg_sales[best_product_index]

print(f"\n(4) 畅销产品：{best_product_name}，日均销售量：{best_product_sales:.2f} 件/天")

# (5) 销售波动分析：计算标准差
sales_std = np.std(sales_data, axis=0)  # 按列计算标准差

print("\n(5) 各产品销售波动性（标准差）：")
for i, product in enumerate(products):
    volatility = "高" if sales_std[i] > np.mean(sales_std) else "低"
    print(f"{product}：{sales_std[i]:.2f}（波动性：{volatility}）")

# (6) 使用Matplotlib进行可视化
print("\n(6) 生成可视化图表...")

# 创建图表
fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(12, 10))
fig.suptitle('销售数据分析报告', fontsize=16, fontweight='bold')

# 图1：日均销售量柱状图
bars1 = ax1.bar(products, daily_avg_sales, color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'])
ax1.set_title('各产品日均销售量', fontweight='bold')
ax1.set_ylabel('销售量（件/天）')
ax1.set_ylim(0, max(daily_avg_sales) * 1.2)

# 在柱状图上显示数值
for bar, value in zip(bars1, daily_avg_sales):
    ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1, 
             f'{value:.1f}', ha='center', va='bottom', fontweight='bold')

# 图2：销售波动性（标准差）
bars2 = ax2.bar(products, sales_std, color=['#E17055', '#74B9FF', '#A29BFE', '#6C5CE7', '#FD79A8'])
ax2.set_title('各产品销售波动性', fontweight='bold')
ax2.set_ylabel('标准差')
ax2.set_ylim(0, max(sales_std) * 1.2)

# 在柱状图上显示数值
for bar, value in zip(bars2, sales_std):
    ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5, 
             f'{value:.1f}', ha='center', va='bottom', fontweight='bold')

# 图3：5天销售趋势线图
for i, product in enumerate(products):
    ax3.plot(days, sales_data[:, i], marker='o', linewidth=2, label=product)
ax3.set_title('5天销售趋势', fontweight='bold')
ax3.set_ylabel('销售量（件）')
ax3.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
ax3.grid(True, alpha=0.3)

# 图4：总销售量饼图
product_totals = np.sum(sales_data, axis=0)  # 每种产品的总销售量
colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7']
wedges, texts, autotexts = ax4.pie(product_totals, labels=products, autopct='%1.1f%%', 
                                   colors=colors, startangle=90)
ax4.set_title('各产品销售占比', fontweight='bold')

# 调整布局
plt.tight_layout()
plt.show()

# 输出汇总报告
print("\n=== 销售数据分析汇总 ===")
print(f"数据维度：{sales_data.shape[0]}天 × {sales_data.shape[1]}种产品")
print(f"总销售额：{total_sales} 件")
print(f"日均总销售：{total_sales/5:.2f} 件/天")
print(f"最佳产品：{best_product_name}（{best_product_sales:.2f} 件/天）")
print(f"最稳定产品：{products[np.argmin(sales_std)]}（标准差：{np.min(sales_std):.2f}）")
print(f"最波动产品：{products[np.argmax(sales_std)]}（标准差：{np.max(sales_std):.2f}）")

print("\n程序执行完成！图表已生成。")