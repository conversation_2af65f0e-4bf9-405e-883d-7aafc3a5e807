# 验证商品销售记录程序
def test_sales_recorder():
    print("=== 商品销售记录程序验证 ===")
    
    # 模拟示例数据
    sample_data = [
        ("笔记本", 1000),
        ("钢笔", 800),
        ("铅笔", 500),
        ("牛奶", 60.5),
        ("矿泉水", 260.5),
        ("书包", 350)
    ]
    
    print("模拟输入示例数据：")
    sales = []  # 存储商品名称和销售额的列表
    
    # 模拟while循环输入过程
    for product_name, sales_amount in sample_data:
        sales.append((product_name, sales_amount))
        print(f"✅ 已添加：{product_name} - {sales_amount}元")
    
    # 显示录入的商品信息
    print(f"\\n共录入 {len(sales)} 件商品：")
    print(f"{'序号':<4} {'商品名称':<12} {'销售额':<10}")
    print("-" * 30)
    for i, (name, amount) in enumerate(sales, 1):
        print(f"{i:<4} {name:<12} {amount:<10.2f}")
    
    # 模拟文件写入过程
    if sales:  # 如果有销售记录
        try:
            # 创建并写入sales.txt文件
            with open('sales.txt', 'w', encoding='utf-8') as file:
                # 写入文件标题
                file.write("=== 当天商品销售明细 ===\\n")
                file.write(f"{'序号':<4} {'商品名称':<12} {'销售额(元)':<12}\\n")
                file.write("-" * 35 + "\\n")
                
                # 初始化总收入
                total_revenue = 0
                
                # 使用for循环遍历销售列表，写入每个商品的信息
                for i, (product_name, sales_amount) in enumerate(sales, 1):
                    # 写入商品明细
                    file.write(f"{i:<4} {product_name:<12} {sales_amount:<12.2f}\\n")
                    # 累计总收入
                    total_revenue += sales_amount
                
                # 写入分隔线和总收入
                file.write("-" * 35 + "\\n")
                file.write(f"当天总收入：{total_revenue:.2f}元\\n")
                file.write(f"商品种类：{len(sales)}种\\n")
            
            print(f"\\n✅ 销售记录已保存到 sales.txt 文件")
            print(f"当天总收入：{total_revenue:.2f}元")
            print(f"商品种类：{len(sales)}种")
            
            # 读取并显示文件内容进行验证
            print("\\n=== sales.txt 文件内容 ===")
            with open('sales.txt', 'r', encoding='utf-8') as file:
                content = file.read()
                print(content)
                
            # 验证计算结果
            expected_total = sum(amount for _, amount in sample_data)
            print(f"验证：预期总收入 {expected_total:.2f}元，实际总收入 {total_revenue:.2f}元")
            if abs(expected_total - total_revenue) < 0.01:
                print("✅ 计算结果正确")
            else:
                print("❌ 计算结果错误")
                
        except Exception as e:
            print(f"❌ 保存文件时发生错误：{e}")
    else:
        print("\\n没有录入任何商品信息")
    
    print("\\n程序验证完成！")

if __name__ == "__main__":
    test_sales_recorder()
