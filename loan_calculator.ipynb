{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 等额本金还款计算器\n", "\n", "根据贷款金额和期限，计算等额本金还款方式下每月的还款明细。\n", "\n", "**计算条件：年利率 4.35%**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 等额本金还款计算器\n", "# 年利率固定为4.35%，计算每月还款明细\n", "\n", "print(\"=== 等额本金还款计算器 ===\")\n", "print(\"年利率：4.35%\")\n", "\n", "# 用户输入贷款信息\n", "loan_amount = float(input(\"请输入贷款金额（元）：\"))\n", "loan_months = int(input(\"请输入贷款期限（月数）：\"))\n", "\n", "# 计算参数\n", "annual_rate = 0.0435  # 年利率4.35%\n", "monthly_rate = annual_rate / 12  # 月利率\n", "monthly_principal = loan_amount / loan_months  # 每月应还本金（固定）\n", "remaining_principal = loan_amount  # 剩余本金\n", "total_payment = 0  # 总还款额\n", "\n", "print(f\"\\n贷款金额：{loan_amount:,.2f} 元\")\n", "print(f\"贷款期限：{loan_months} 个月\")\n", "print(f\"年利率：{annual_rate:.2%}\")\n", "print(f\"月利率：{monthly_rate:.4%}\")\n", "print(f\"每月固定本金：{monthly_principal:,.2f} 元\")\n", "\n", "print(\"\\n=== 每月还款明细 ===\")\n", "print(f\"{'月份':<4} {'应还本金':<12} {'应还利息':<12} {'月还款额':<12} {'剩余本金':<12}\")\n", "print(\"-\" * 60)\n", "\n", "# 计算每月还款明细\n", "for month in range(1, loan_months + 1):\n", "    # 当月应还利息 = 剩余本金 × 月利率\n", "    monthly_interest = remaining_principal * monthly_rate\n", "    \n", "    # 当月总还款额 = 固定本金 + 当月利息\n", "    monthly_payment = monthly_principal + monthly_interest\n", "    \n", "    # 累计总还款额\n", "    total_payment += monthly_payment\n", "    \n", "    # 输出当月明细\n", "    print(f\"{month:<4} {monthly_principal:<12,.2f} {monthly_interest:<12,.2f} {monthly_payment:<12,.2f} {remaining_principal:<12,.2f}\")\n", "    \n", "    # 更新剩余本金\n", "    remaining_principal -= monthly_principal\n", "\n", "print(\"-\" * 60)\n", "print(f\"总还款额：{total_payment:,.2f} 元\")\n", "print(f\"总利息：{total_payment - loan_amount:,.2f} 元\")\n", "print(\"还款完成！\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 等额本金还款说明\n", "\n", "### 计算公式：\n", "- **每月应还本金** = 贷款总额 ÷ 还款月数（固定不变）\n", "- **每月应还利息** = 剩余本金 × 月利率\n", "- **每月还款总额** = 每月应还本金 + 每月应还利息\n", "- **月利率** = 年利率 ÷ 12\n", "\n", "### 特点：\n", "- ✅ 每月还款本金固定\n", "- ✅ 每月利息递减（因剩余本金减少）\n", "- ✅ 每月总还款额递减\n", "- ✅ 前期还款压力大，后期压力小\n", "- ✅ 总利息比等额本息少\n", "\n", "### 示例：\n", "**贷款100万元，期限24个月，年利率4.35%**\n", "- 每月固定本金：41,666.67元\n", "- 第1月利息：3,625.00元，总还款：45,291.67元\n", "- 第24月利息：150.94元，总还款：41,817.61元"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}