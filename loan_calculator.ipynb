
print("=== 等额本金还款计算器 ===")
print("年利率：4.35%")
# 用户输入贷款信息
loan_amount = float(input("请输入贷款金额（元）："))
loan_months = int(input("请输入贷款期限（月数）："))
# 计算参数
annual_rate = 0.0435  # 年利率4.35%
monthly_rate = annual_rate / 12  # 月利率
monthly_principal = loan_amount / loan_months  # 每月应还本金（固定）
remaining_principal = loan_amount  # 剩余本金
total_payment = 0  # 总还款额
print(f"\n贷款金额：{loan_amount:,.2f} 元")
print(f"贷款期限：{loan_months} 个月")
print(f"年利率：{annual_rate:.2%}")
print(f"月利率：{monthly_rate:.4%}")
print(f"每月固定本金：{monthly_principal:,.2f} 元")
print("\n=== 每月还款明细 ===")
print(f"{'月份':<4} {'应还本金':<12} {'应还利息':<12} {'月还款额':<12} {'剩余本金':<12}")
print("-" * 60)
# 计算每月还款明细
for month in range(1, loan_months + 1):
    # 当月应还利息 = 剩余本金 × 月利率
    monthly_interest = remaining_principal * monthly_rate
    # 当月总还款额 = 固定本金 + 当月利息
    monthly_payment = monthly_principal + monthly_interest
    # 累计总还款额
    total_payment += monthly_payment
    # 输出当月明细
    print(f"{month:<4} {monthly_principal:<12,.2f} {monthly_interest:<12,.2f} {monthly_payment:<12,.2f} {remaining_principal:<12,.2f}")
    # 更新剩余本金
    remaining_principal -= monthly_principal
print("-" * 60)
print(f"总还款额：{total_payment:,.2f} 元")
print(f"总利息：{total_payment - loan_amount:,.2f} 元")
print("还款完成！")