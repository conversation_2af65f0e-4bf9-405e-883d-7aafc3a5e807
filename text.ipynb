pip install baostock





stock_list = [['600000','浦发银行',11.52,11.54,11.61,11.40],
             ['600036','招商银行',37.27,36.84,37.44,36.52],
             ['600048','保利地产',16.87,16.61,16.93,16.45],
             ['000001','平安银行',14.73,14.49,14.68,14.40],
             ['000063','中兴通讯',41.24,40.98,41.59,40.67],
             ['300142','沃森生物',60.40,64.65,65.59,59.55]]

stock_index = ['股票代码','股票名称','开盘价','最高价','最低价','收盘价']

stock_index_str = input('请输入股票的序号：')
stock_index_number = int(stock_index_str)
stock_info = stock_list[stock_index_number-1]
print('代码：{}名称：{}'.format(stock_info[0],stock_info[1]))
print('开盘价：{}收盘价：{}'.format(stock_info[2],stock_info[3],))
print('最高价：{}最低价：{}'.format(stock_info[4],stock_info[5]))




# 假设你有一个包含人员姓名和身份证号码的二维列表：
people_info = [
    ["张三", "110101199001011234","男"],
    ["李四", "110102199102022345","女"],
    ["王五", "110103199203033456","男"],
    ["赵六", "110104199304044567","女"],
    ["钱七", "110105199405055678","男"]
]


请通过索引的方式完成以下任务：
获取第一个人的姓名、身份证号码和性别。
获取第三个人的姓名。
获取所有人的身份证号码。
请判断此数据包含多少人的数据信息



# 假设你有一个包含人员姓名和身份证号码的二维列表：
people_info = [
    ["张三", "110101199001011234", "男"],
    ["李四", "110102199102022345", "女"],
    ["王五", "110103199203033456", "男"],
    ["赵六", "110104199304044567", "女"],
    ["钱七", "110105199405055678", "男"]
]

# 获取第一个人的姓名、身份证号码和性别
first_person_info = people_info[0]
first_person_name = first_person_info[0]
first_person_id = first_person_info[1]
first_person_gender = first_person_info[2]
print("第一个人的姓名：", first_person_name)
print("第一个人的身份证号码：", first_person_id)
print("第一个人的性别：", first_person_gender)

# 获取第三个人的姓名
third_person_name = people_info[2][0]
print("第三个人的姓名：", third_person_name)

# 获取所有人的身份证号码
all_ids = [person_info[1] for person_info in people_info]
print("所有人的身份证号码：", all_ids)

# 判断此数据包含多少人的数据信息
num_people = len(people_info)
print("此数据包含", num_people, "人的数据信息")


#练习：列表常用函数和方法实操：
# 1.创建了一个变量名为：mixed_lst的列表
mixed_lst = [4, "apple", 9.3, [1, 2, 3], "10"]
  
# 2.请计算mixed_lst列表长度  
print(len(mixed_lst))

# 3.通过索引获取mixed_lst列表中第3个元素的值  
print(mixed_lst[2])
  
# 4.通过分片/切片获取mixed_lst列表中第2到第4个元素（不包括第5个）
print(mixed_lst[1:4])

# 5.通过分片/切片获取mixed_lst列表中第4元元素[1,2,3]中的第2个元素
print(mixed_lst[3][1])
  
# 6.在mixed_lst列表的末尾添加2个元素:"orange"和103。  
mixed_lst.append("orange")
mixed_lst.append(103)
print(mixed_lst)

# 7.在mixed_lst列表的末尾添加一个元素:[4,5,6]
mixed_lst.append([4, 5, 6])
print(mixed_lst)
  
# 8.在mixed_lst列表的第4个位置插入元素'Python'  
mixed_lst.insert(3, 'Python')
print(mixed_lst)

# 9.从mixed_lst列表中移除元素"orange"
mixed_lst.remove("orange")
print(mixed_lst)
  
# 10.查找mixed_lst列表中元素"apple"的索引位置  
print(mixed_lst.index("apple"))

# 11.对mixed_lst列表当前所有的元素重新按反向顺序进行排列
mixed_lst.reverse()
print(mixed_lst)

final_amt = float(input('请输入复利终值: '))
amt_list = [final_amt]
rate = 0.03
pre_amt = amt_list[-1] / (1 + rate)
amt_list.append(pre_amt)
pre_amt = amt_list[-1] / (1 + rate)
amt_list.append(pre_amt)
pre_amt = amt_list[-1] / (1 + rate)
amt_list.append(pre_amt)
pre_amt = amt_list[-1] / (1 + rate)
amt_list.append(pre_amt)
amt_list.reverse()
print('-' * 35)
print('需投入本金为: {:.2f}元}'format(amt_list[0]))
print(第二年本息余额： {:.2f}元'.format(amt_list[1])
print(第三年本息余额： {:.2f}元'.format(amt_list[2])
print(第四年本息余额： {:.2f}元'.format(amt_list[3])
print(第五年本息余额： {:.2f}元'.format(amt_list[4])

final_amt = float(input('请输入复利终值: '))
amt_list = [final_amt]
rate = 0.03
pre_amt = amt_list[-1] / (1 + rate)
amt_list.append(pre_amt)
pre_amt = amt_list[-1] / (1 + rate)
amt_list.append(pre_amt)
pre_amt = amt_list[-1] / (1 + rate)
amt_list.append(pre_amt)
pre_amt = amt_list[-1] / (1 + rate)
amt_list.append(pre_amt)
amt_list.reverse()
print('-' * 35)
print('需投入本金为: {:.2f}元'.format(amt_list[0]))
print('第二年本息余额： {:.2f}元'.format(amt_list[1]))
print('第三年本息余额： {:.2f}元'.format(amt_list[2]))
print('第四年本息余额： {:.2f}元'.format(amt_list[3]))
print('第五年本息余额： {:.2f}元'.format(amt_list[4]))

# 输入人口数量变量（基准年）
population_number = int(input("请输入基准年的人口数量："))

# 创建列表保存各年人口数量
population_list = [population_number]

# 人口数量增长率
population_rate = float(input("请输入人口数量增长率（例如0.05表示5%）："))

# 预测未来3年的人口数量
for i in range(3):
    population_next_number = population_list[-1] * (1 + population_rate)
    population_list.append(population_next_number)

# 输出结果
print('-' * 35)
print("未来3年的人口数量预测：")
for i, population in enumerate(population_list[1:]):
    print(f"第{i+1}年：{population:.2f}人")


# 输入人口数量变量（基准年）
population_number = float(input("请输入基准年的人口数量："))

# 创建列表保存各年人口数量
population_list = [population_number]

# 人口数量增长率
population_rate = float(input("请输入人口数量增长率(例如0.05表示5%):"))

# 预测未来3年的人口数量
population_next_number = population_list[-1] * (1 + population_rate)
population_list.append(population_next_number)
population_next_number = population_list[-1] * (1 + population_rate)
population_list.append(population_next_number)
population_next_number = population_list[-1] * (1 + population_rate)
population_list.append(population_next_number)
print(population_list)
print('基准年人口数量:{:.2f}}').format(population_list[0])
print('第1年人口数量:{:.2f}}').format(population_list[1])
print('第2年人口数量:{:.2f}}').format(population_list[2])
print('第3年人口数量:{:.2f}}').format(population_list[3])






# 输入人口数量变量（基准年）
population_number = float(input("请输入基准年的人口数量："))

# 创建列表保存各年人口数量
population_list = [population_number]

# 人口数量增长率
population_rate = float(input("请输入人口数量增长率(例如0.05表示5%):"))

# 预测未来3年的人口数量
population_next_number = population_list[-1] * (1 + population_rate)
population_list.append(population_next_number)
population_next_number = population_list[-1] * (1 + population_rate)
population_list.append(population_next_number)
population_next_number = population_list[-1] * (1 + population_rate)
population_list.append(population_next_number)
print(population_list)

print('基准年人口数量:{:.2f}人'.format(population_list[0]))
print('第1年人口数量:{:.2f}人'.format(population_list[1]))
print('第2年人口数量:{:.2f}人'.format(population_list[2]))
print('第3年人口数量:{:.2f}人'.format(population_list[3]))

economics_trade = {
    "202210105035": {"姓名": "朱燕柳", "成绩": 85},
    "202210105097": {"姓名": "叶自雅", "成绩": 92},
    "202210137004": {"姓名": "罗锦昊", "成绩": 78},
    "202210137006": {"姓名": "容燕萍", "成绩": 89},
    "202210137010": {"姓名": "倪诗雨", "成绩": 76},
    "202210137012": {"姓名": "杨阳", "成绩": 94},
    "202210137013": {"姓名": "李冰梅", "成绩": 82},
    "202210137015": {"姓名": "陈茵茵", "成绩": 90},
    "202210137017": {"姓名": "李素芬", "成绩": 79},
    "202210137018": {"姓名": "杨弈昊", "成绩": 87},
    "202210137019": {"姓名": "林华鑫", "成绩": 95},
    "202210137021": {"姓名": "盘格玲", "成绩": 81},
    "202210137024": {"姓名": "李淑菲", "成绩": 93},
    "202210137026": {"姓名": "陈慧琦", "成绩": 86},
    "202210137028": {"姓名": "陆冠荣", "成绩": 77},
    "202210137029": {"姓名": "冯婉怡", "成绩": 91},
    "202210137030": {"姓名": "方腾", "成绩": 84},
    "202210137033": {"姓名": "王睿", "成绩": 96},
    "202210137036": {"姓名": "何昕潞", "成绩": 80},
    "202210137037": {"姓名": "陈嘉欣", "成绩": 97},
    "202210137039": {"姓名": "洪宁", "成绩": 88},
    "202210137040": {"姓名": "莫诗雅", "成绩": 75},
    "202210137042": {"姓名": "麦雯静", "成绩": 99},
    "202210137044": {"姓名": "郑乐琪", "成绩": 83},
    "202210137048": {"姓名": "陈芊卉", "成绩": 98},
    "202210137049": {"姓名": "韦雨彤", "成绩": 85},
    "202210137051": {"姓名": "刘锦妍", "成绩": 74},
    "202210137055": {"姓名": "杨一鸣", "成绩": 90},
    "202210137056": {"姓名": "赵富乾", "成绩": 82},
    "202210137059": {"姓名": "葛罗明", "成绩": 91},
    "202210137061": {"姓名": "张宇森", "成绩": 87},
    "202210137065": {"姓名": "赵巧", "成绩": 76},
    "202210137068": {"姓名": "廖俊辉", "成绩": 93},
    "202210137070": {"姓名": "毛梓航", "成绩": 89},
       "202310137067": {"姓名": "张某某", "成绩": 88},
    "202310137068": {"姓名": "李某某", "成绩": 92},
}

# 1. 在字典中修改张某某的成绩状态为”缺考”
# 2. 请将你的成绩修改为100
# 3. 请将李某某姓名及成绩进行删除
# 4. 使用for循环遍历并输出当前字典
# 5. 统计当前总人数并输出
# 6. 从键盘输入input()一个同学的学号,显示该同学的成绩,如字典中无此学号的同学显示”没找到该同学”,使用if/else语句。
# 7. 请统计全班80分以上的同学有多少名？


economics_trade = {
    # ... 原有字典数据保持不变 ...
}

# 1. 修改张某某的成绩状态为"缺考"
economics_trade["202310137067"]["成绩"] = "缺考"

# 2. 将你的成绩修改为100 (假设你的学号是202210137068)
# 请将下面的学号替换为你实际的学号
your_id = "202210137068"  
if your_id in economics_trade:
    economics_trade[your_id]["成绩"] = 100

# 3. 删除李某某的姓名及成绩
del economics_trade["202310137068"]

# 4. 使用for循环遍历并输出当前字典
print("当前所有学生信息：")
for student_id, info in economics_trade.items():
    print(f"学号: {student_id}, 姓名: {info['姓名']}, 成绩: {info['成绩']}")

# 5. 统计当前总人数并输出
total_students = len(economics_trade)
print(f"\n当前总人数: {total_students}人")

# 6. 从键盘输入学号查询成绩
search_id = input("\n请输入要查询的学号: ")
if search_id in economics_trade:
    student = economics_trade[search_id]
    print(f"找到该同学: 姓名: {student['姓名']}, 成绩: {student['成绩']}")
else:
    print("没找到该同学")

# 7. 统计全班80分以上的同学数量
high_score_count = 0
for info in economics_trade.values():
    if isinstance(info["成绩"], int) and info["成绩"] >= 80:
        high_score_count += 1

print(f"\n全班80分以上的同学有: {high_score_count}名")



economics_trade = {
    "202210105035": {"姓名": "朱燕柳", "成绩": 85},
    "202210105097": {"姓名": "叶自雅", "成绩": 92},
    "202210137004": {"姓名": "罗锦昊", "成绩": 78},
    "202210137006": {"姓名": "容燕萍", "成绩": 89},
    "202210137010": {"姓名": "倪诗雨", "成绩": 76},
    "202210137012": {"姓名": "杨阳", "成绩": 94},
    "202210137013": {"姓名": "李冰梅", "成绩": 82},
    "202210137015": {"姓名": "陈茵茵", "成绩": 90},
    "202210137017": {"姓名": "李素芬", "成绩": 79},
    "202210137018": {"姓名": "杨弈昊", "成绩": 87},
    "202210137019": {"姓名": "林华鑫", "成绩": 95},
    "202210137021": {"姓名": "盘格玲", "成绩": 81},
    "202210137024": {"姓名": "李淑菲", "成绩": 93},
    "202210137026": {"姓名": "陈慧琦", "成绩": 86},
    "202210137028": {"姓名": "陆冠荣", "成绩": 77},
    "202210137029": {"姓名": "冯婉怡", "成绩": 91},
    "202210137030": {"姓名": "方腾", "成绩": 84},
    "202210137033": {"姓名": "王睿", "成绩": 96},
    "202210137036": {"姓名": "何昕潞", "成绩": 80},
    "202210137037": {"姓名": "陈嘉欣", "成绩": 97},
    "202210137039": {"姓名": "洪宁", "成绩": 88},
    "202210137040": {"姓名": "莫诗雅", "成绩": 75},
    "202210137042": {"姓名": "麦雯静", "成绩": 99},
    "202210137044": {"姓名": "郑乐琪", "成绩": 83},
    "202210137048": {"姓名": "陈芊卉", "成绩": 98},
    "202210137049": {"姓名": "韦雨彤", "成绩": 85},
    "202210137051": {"姓名": "刘锦妍", "成绩": 74},
    "202210137055": {"姓名": "杨一鸣", "成绩": 90},
    "202210137056": {"姓名": "赵富乾", "成绩": 82},
    "202210137059": {"姓名": "葛罗明", "成绩": 91},
    "202210137061": {"姓名": "张宇森", "成绩": 87},
    "202210137065": {"姓名": "赵巧", "成绩": 76},
    "202210137068": {"姓名": "廖俊辉", "成绩": 93},
    "202210137070": {"姓名": "毛梓航", "成绩": 89},
       "202310137067": {"姓名": "张某某", "成绩": 88},
    "202310137068": {"姓名": "李某某", "成绩": 92},
}

# 1. 在字典中修改张某某的成绩状态为”缺考”
# 2. 请将你的成绩修改为100
# 3. 请将李某某姓名及成绩进行删除
# 4. 使用for循环遍历并输出当前字典
# 5. 统计当前总人数并输出
# 6. 从键盘输入input()一个同学的学号,显示该同学的成绩,如字典中无此学号的同学显示”没找到该同学”,使用if/else语句。
# 7. 请统计全班80分以上的同学有多少名？
# 1. 修改张某某的成绩状态为"缺考"
economics_trade["202310137067"]["成绩"] = "缺考"

# 2. 将你的成绩修改为100 (假设你的学号是202210137068)
your_id = "202210137068"  # 请替换为你实际的学号
if your_id in economics_trade:
    economics_trade[your_id]["成绩"] = 100

# 3. 删除李某某的姓名及成绩
if "202310137068" in economics_trade:
    del economics_trade["202310137068"]

# 4. 使用for循环遍历并输出当前字典
print("当前所有学生信息：")
for student_id, info in economics_trade.items():
    print(f"学号: {student_id}, 姓名: {info['姓名']}, 成绩: {info['成绩']}")

# 5. 统计当前总人数并输出
total_students = len(economics_trade)
print(f"\n当前总人数: {total_students}人")

# 6. 从键盘输入学号查询成绩
search_id = input("\n请输入要查询的学号: ")
if search_id in economics_trade:
    student = economics_trade[search_id]
    print(f"找到该同学: 姓名: {student['姓名']}, 成绩: {student['成绩']}")
else:
    print("没找到该同学")

# 7. 统计全班80分以上的同学数量
high_score_count = 0
for info in economics_trade.values():
    if isinstance(info["成绩"], int) and info["成绩"] >= 80:
        high_score_count += 1

print(f"\n全班80分以上的同学有: {high_score_count}名")
