{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Looking in indexes: https://pypi.mirrors.ustc.edu.cn/simple\n", "Requirement already satisfied: baostock in ./venv/lib/python3.12/site-packages (0.8.9)\n", "Requirement already satisfied: pandas>=0.18.0 in ./venv/lib/python3.12/site-packages (from baostock) (2.2.3)\n", "Requirement already satisfied: numpy>=1.26.0 in ./venv/lib/python3.12/site-packages (from pandas>=0.18.0->baostock) (2.2.3)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in ./venv/lib/python3.12/site-packages (from pandas>=0.18.0->baostock) (2.9.0.post0)\n", "Requirement already satisfied: pytz>=2020.1 in ./venv/lib/python3.12/site-packages (from pandas>=0.18.0->baostock) (2025.1)\n", "Requirement already satisfied: tzdata>=2022.7 in ./venv/lib/python3.12/site-packages (from pandas>=0.18.0->baostock) (2025.1)\n", "Requirement already satisfied: six>=1.5 in ./venv/lib/python3.12/site-packages (from python-dateutil>=2.8.2->pandas>=0.18.0->baostock) (1.17.0)\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["pip install baostock"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["代码：600000名称：浦发银行\n", "开盘价：11.52收盘价：11.54\n", "最高价：11.61最低价：11.4\n"]}], "source": ["stock_list = [['600000','浦发银行',11.52,11.54,11.61,11.40],\n", "             ['600036','招商银行',37.27,36.84,37.44,36.52],\n", "             ['600048','保利地产',16.87,16.61,16.93,16.45],\n", "             ['000001','平安银行',14.73,14.49,14.68,14.40],\n", "             ['000063','中兴通讯',41.24,40.98,41.59,40.67],\n", "             ['300142','沃森生物',60.40,64.65,65.59,59.55]]\n", "\n", "stock_index = ['股票代码','股票名称','开盘价','最高价','最低价','收盘价']\n", "\n", "stock_index_str = input('请输入股票的序号：')\n", "stock_index_number = int(stock_index_str)\n", "stock_info = stock_list[stock_index_number-1]\n", "print('代码：{}名称：{}'.format(stock_info[0],stock_info[1]))\n", "print('开盘价：{}收盘价：{}'.format(stock_info[2],stock_info[3],))\n", "print('最高价：{}最低价：{}'.format(stock_info[4],stock_info[5]))\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"ename": "SyntaxError", "evalue": "invalid character '：' (U+FF1A) (1114642750.py, line 11)", "output_type": "error", "traceback": ["\u001b[0;36m  Cell \u001b[0;32mIn[20], line 11\u001b[0;36m\u001b[0m\n\u001b[0;31m    请通过索引的方式完成以下任务：\u001b[0m\n\u001b[0m                  ^\u001b[0m\n\u001b[0;31mSyntaxError\u001b[0m\u001b[0;31m:\u001b[0m invalid character '：' (U+FF1A)\n"]}], "source": ["# 假设你有一个包含人员姓名和身份证号码的二维列表：\n", "people_info = [\n", "    [\"张三\", \"110101199001011234\",\"男\"],\n", "    [\"李四\", \"110102199102022345\",\"女\"],\n", "    [\"王五\", \"110103199203033456\",\"男\"],\n", "    [\"赵六\", \"110104199304044567\",\"女\"],\n", "    [\"钱七\", \"110105199405055678\",\"男\"]\n", "]\n", "\n", "\n", "请通过索引的方式完成以下任务：\n", "获取第一个人的姓名、身份证号码和性别。\n", "获取第三个人的姓名。\n", "获取所有人的身份证号码。\n", "请判断此数据包含多少人的数据信息\n", "\n"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["第一个人的姓名： 张三\n", "第一个人的身份证号码： 110101199001011234\n", "第一个人的性别： 男\n", "第三个人的姓名： 王五\n", "所有人的身份证号码： ['110101199001011234', '110102199102022345', '110103199203033456', '110104199304044567', '110105199405055678']\n", "此数据包含 5 人的数据信息\n"]}], "source": ["# 假设你有一个包含人员姓名和身份证号码的二维列表：\n", "people_info = [\n", "    [\"张三\", \"110101199001011234\", \"男\"],\n", "    [\"李四\", \"110102199102022345\", \"女\"],\n", "    [\"王五\", \"110103199203033456\", \"男\"],\n", "    [\"赵六\", \"110104199304044567\", \"女\"],\n", "    [\"钱七\", \"110105199405055678\", \"男\"]\n", "]\n", "\n", "# 获取第一个人的姓名、身份证号码和性别\n", "first_person_info = people_info[0]\n", "first_person_name = first_person_info[0]\n", "first_person_id = first_person_info[1]\n", "first_person_gender = first_person_info[2]\n", "print(\"第一个人的姓名：\", first_person_name)\n", "print(\"第一个人的身份证号码：\", first_person_id)\n", "print(\"第一个人的性别：\", first_person_gender)\n", "\n", "# 获取第三个人的姓名\n", "third_person_name = people_info[2][0]\n", "print(\"第三个人的姓名：\", third_person_name)\n", "\n", "# 获取所有人的身份证号码\n", "all_ids = [person_info[1] for person_info in people_info]\n", "print(\"所有人的身份证号码：\", all_ids)\n", "\n", "# 判断此数据包含多少人的数据信息\n", "num_people = len(people_info)\n", "print(\"此数据包含\", num_people, \"人的数据信息\")"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"ename": "TypeError", "evalue": "'int' object is not callable", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mTypeError\u001b[0m                                 Traceback (most recent call last)", "\u001b[1;32m/cloudide/workspace/Python--diyike/text.ipynb Cell 6\u001b[0m line \u001b[0;36m6\n\u001b[1;32m      <a href='vscode-notebook-cell://icube%2Bicube/cloudide/workspace/Python--diyike/text.ipynb#W5sdnNjb2RlLXJlbW90ZQ%3D%3D?line=2'>3</a>\u001b[0m mixed_lst \u001b[39m=\u001b[39m [\u001b[39m4\u001b[39m, \u001b[39m\"\u001b[39m\u001b[39mapple\u001b[39m\u001b[39m\"\u001b[39m, \u001b[39m9.3\u001b[39m, [\u001b[39m1\u001b[39m, \u001b[39m2\u001b[39m, \u001b[39m3\u001b[39m], \u001b[39m\"\u001b[39m\u001b[39m10\u001b[39m\u001b[39m\"\u001b[39m]\n\u001b[1;32m      <a href='vscode-notebook-cell://icube%2Bicube/cloudide/workspace/Python--diyike/text.ipynb#W5sdnNjb2RlLXJlbW90ZQ%3D%3D?line=4'>5</a>\u001b[0m \u001b[39m# 2.请计算mixed_lst列表长度  \u001b[39;00m\n\u001b[0;32m----> <a href='vscode-notebook-cell://icube%2Bicube/cloudide/workspace/Python--diyike/text.ipynb#W5sdnNjb2RlLXJlbW90ZQ%3D%3D?line=5'>6</a>\u001b[0m \u001b[39mprint\u001b[39;49m(\u001b[39mlen\u001b[39;49m(mixed_lst))\n\u001b[1;32m      <a href='vscode-notebook-cell://icube%2Bicube/cloudide/workspace/Python--diyike/text.ipynb#W5sdnNjb2RlLXJlbW90ZQ%3D%3D?line=7'>8</a>\u001b[0m \u001b[39m# 3.通过索引获取mixed_lst列表中第3个元素的值  \u001b[39;00m\n\u001b[1;32m      <a href='vscode-notebook-cell://icube%2Bicube/cloudide/workspace/Python--diyike/text.ipynb#W5sdnNjb2RlLXJlbW90ZQ%3D%3D?line=8'>9</a>\u001b[0m \u001b[39mprint\u001b[39m(mixed_lst[\u001b[39m2\u001b[39m])\n", "\u001b[0;31mTypeError\u001b[0m: 'int' object is not callable"]}], "source": ["\n", "#练习：列表常用函数和方法实操：\n", "# 1.创建了一个变量名为：mixed_lst的列表\n", "mixed_lst = [4, \"apple\", 9.3, [1, 2, 3], \"10\"]\n", "  \n", "# 2.请计算mixed_lst列表长度  \n", "print(len(mixed_lst))\n", "\n", "# 3.通过索引获取mixed_lst列表中第3个元素的值  \n", "print(mixed_lst[2])\n", "  \n", "# 4.通过分片/切片获取mixed_lst列表中第2到第4个元素（不包括第5个）\n", "print(mixed_lst[1:4])\n", "\n", "# 5.通过分片/切片获取mixed_lst列表中第4元元素[1,2,3]中的第2个元素\n", "print(mixed_lst[3][1])\n", "  \n", "# 6.在mixed_lst列表的末尾添加2个元素:\"orange\"和103。  \n", "mixed_lst.append(\"orange\")\n", "mixed_lst.append(103)\n", "print(mixed_lst)\n", "\n", "# 7.在mixed_lst列表的末尾添加一个元素:[4,5,6]\n", "mixed_lst.append([4, 5, 6])\n", "print(mixed_lst)\n", "  \n", "# 8.在mixed_lst列表的第4个位置插入元素'Python'  \n", "mixed_lst.insert(3, '<PERSON>')\n", "print(mixed_lst)\n", "\n", "# 9.从mixed_lst列表中移除元素\"orange\"\n", "mixed_lst.remove(\"orange\")\n", "print(mixed_lst)\n", "  \n", "# 10.查找mixed_lst列表中元素\"apple\"的索引位置  \n", "print(mixed_lst.index(\"apple\"))\n", "\n", "# 11.对mixed_lst列表当前所有的元素重新按反向顺序进行排列\n", "mixed_lst.reverse()\n", "print(mixed_lst)"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"ename": "SyntaxError", "evalue": "invalid character '：' (U+FF1A) (3371666104.py, line 15)", "output_type": "error", "traceback": ["\u001b[0;36m  Cell \u001b[0;32mIn[2], line 15\u001b[0;36m\u001b[0m\n\u001b[0;31m    print(第二年本息余额： {:.2f}元'.format(amt_list[1])\u001b[0m\n\u001b[0m                 ^\u001b[0m\n\u001b[0;31mSyntaxError\u001b[0m\u001b[0;31m:\u001b[0m invalid character '：' (U+FF1A)\n"]}], "source": ["final_amt = float(input('请输入复利终值: '))\n", "amt_list = [final_amt]\n", "rate = 0.03\n", "pre_amt = amt_list[-1] / (1 + rate)\n", "amt_list.append(pre_amt)\n", "pre_amt = amt_list[-1] / (1 + rate)\n", "amt_list.append(pre_amt)\n", "pre_amt = amt_list[-1] / (1 + rate)\n", "amt_list.append(pre_amt)\n", "pre_amt = amt_list[-1] / (1 + rate)\n", "amt_list.append(pre_amt)\n", "amt_list.reverse()\n", "print('-' * 35)\n", "print('需投入本金为: {:.2f}元}'format(amt_list[0]))\n", "print(第二年本息余额： {:.2f}元'.format(amt_list[1])\n", "print(第三年本息余额： {:.2f}元'.format(amt_list[2])\n", "print(第四年本息余额： {:.2f}元'.format(amt_list[3])\n", "print(第五年本息余额： {:.2f}元'.format(amt_list[4])"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["-----------------------------------\n", "需投入本金为: 26654.61元\n", "第二年本息余额： 27454.25元\n", "第三年本息余额： 28277.88元\n", "第四年本息余额： 29126.21元\n", "第五年本息余额： 30000.00元\n"]}], "source": ["final_amt = float(input('请输入复利终值: '))\n", "amt_list = [final_amt]\n", "rate = 0.03\n", "pre_amt = amt_list[-1] / (1 + rate)\n", "amt_list.append(pre_amt)\n", "pre_amt = amt_list[-1] / (1 + rate)\n", "amt_list.append(pre_amt)\n", "pre_amt = amt_list[-1] / (1 + rate)\n", "amt_list.append(pre_amt)\n", "pre_amt = amt_list[-1] / (1 + rate)\n", "amt_list.append(pre_amt)\n", "amt_list.reverse()\n", "print('-' * 35)\n", "print('需投入本金为: {:.2f}元'.format(amt_list[0]))\n", "print('第二年本息余额： {:.2f}元'.format(amt_list[1]))\n", "print('第三年本息余额： {:.2f}元'.format(amt_list[2]))\n", "print('第四年本息余额： {:.2f}元'.format(amt_list[3]))\n", "print('第五年本息余额： {:.2f}元'.format(amt_list[4]))"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["-----------------------------------\n", "未来3年的人口数量预测：\n", "第1年：1050.00人\n", "第2年：1102.50人\n", "第3年：1157.62人\n"]}], "source": ["# 输入人口数量变量（基准年）\n", "population_number = int(input(\"请输入基准年的人口数量：\"))\n", "\n", "# 创建列表保存各年人口数量\n", "population_list = [population_number]\n", "\n", "# 人口数量增长率\n", "population_rate = float(input(\"请输入人口数量增长率（例如0.05表示5%）：\"))\n", "\n", "# 预测未来3年的人口数量\n", "for i in range(3):\n", "    population_next_number = population_list[-1] * (1 + population_rate)\n", "    population_list.append(population_next_number)\n", "\n", "# 输出结果\n", "print('-' * 35)\n", "print(\"未来3年的人口数量预测：\")\n", "for i, population in enumerate(population_list[1:]):\n", "    print(f\"第{i+1}年：{population:.2f}人\")\n"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[10000.0, 10500.0, 11025.0, 11576.25]\n", "基准年人口数量:{:.2f}}\n"]}, {"ename": "AttributeError", "evalue": "'NoneType' object has no attribute 'format'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "\u001b[1;32m/cloudide/workspace/Python--diyike/text.ipynb Cell 13\u001b[0m line \u001b[0;36m1\n\u001b[1;32m     <a href='vscode-notebook-cell://icube%2Bicube/cloudide/workspace/Python--diyike/text.ipynb#X15sdnNjb2RlLXJlbW90ZQ%3D%3D?line=15'>16</a>\u001b[0m population_list\u001b[39m.\u001b[39mappend(population_next_number)\n\u001b[1;32m     <a href='vscode-notebook-cell://icube%2Bicube/cloudide/workspace/Python--diyike/text.ipynb#X15sdnNjb2RlLXJlbW90ZQ%3D%3D?line=16'>17</a>\u001b[0m \u001b[39mprint\u001b[39m(population_list)\n\u001b[0;32m---> <a href='vscode-notebook-cell://icube%2Bicube/cloudide/workspace/Python--diyike/text.ipynb#X15sdnNjb2RlLXJlbW90ZQ%3D%3D?line=17'>18</a>\u001b[0m \u001b[39mprint\u001b[39;49m(\u001b[39m'\u001b[39;49m\u001b[39m基准年人口数量:\u001b[39;49m\u001b[39m{:.2f}\u001b[39;49;00m\u001b[39m}\u001b[39;49m\u001b[39m'\u001b[39;49m)\u001b[39m.\u001b[39;49mformat(population_list[\u001b[39m0\u001b[39m])\n\u001b[1;32m     <a href='vscode-notebook-cell://icube%2Bicube/cloudide/workspace/Python--diyike/text.ipynb#X15sdnNjb2RlLXJlbW90ZQ%3D%3D?line=18'>19</a>\u001b[0m \u001b[39mprint\u001b[39m(\u001b[39m'\u001b[39m\u001b[39m第1年人口数量:\u001b[39m\u001b[39m{:.2f}\u001b[39;00m\u001b[39m}\u001b[39m\u001b[39m'\u001b[39m)\u001b[39m.\u001b[39mformat(population_list[\u001b[39m1\u001b[39m])\n\u001b[1;32m     <a href='vscode-notebook-cell://icube%2Bicube/cloudide/workspace/Python--diyike/text.ipynb#X15sdnNjb2RlLXJlbW90ZQ%3D%3D?line=19'>20</a>\u001b[0m \u001b[39mprint\u001b[39m(\u001b[39m'\u001b[39m\u001b[39m第2年人口数量:\u001b[39m\u001b[39m{:.2f}\u001b[39;00m\u001b[39m}\u001b[39m\u001b[39m'\u001b[39m)\u001b[39m.\u001b[39mformat(population_list[\u001b[39m2\u001b[39m])\n", "\u001b[0;31mAttributeError\u001b[0m: 'NoneType' object has no attribute 'format'"]}], "source": ["# 输入人口数量变量（基准年）\n", "population_number = float(input(\"请输入基准年的人口数量：\"))\n", "\n", "# 创建列表保存各年人口数量\n", "population_list = [population_number]\n", "\n", "# 人口数量增长率\n", "population_rate = float(input(\"请输入人口数量增长率(例如0.05表示5%):\"))\n", "\n", "# 预测未来3年的人口数量\n", "population_next_number = population_list[-1] * (1 + population_rate)\n", "population_list.append(population_next_number)\n", "population_next_number = population_list[-1] * (1 + population_rate)\n", "population_list.append(population_next_number)\n", "population_next_number = population_list[-1] * (1 + population_rate)\n", "population_list.append(population_next_number)\n", "print(population_list)\n", "print('基准年人口数量:{:.2f}}').format(population_list[0])\n", "print('第1年人口数量:{:.2f}}').format(population_list[1])\n", "print('第2年人口数量:{:.2f}}').format(population_list[2])\n", "print('第3年人口数量:{:.2f}}').format(population_list[3])\n", "\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1000.0, 1050.0, 1102.5, 1157.625]\n", "基准年人口数量:1000.00人\n", "第1年人口数量:1050.00人\n", "第2年人口数量:1102.50人\n", "第3年人口数量:1157.62人\n"]}], "source": ["# 输入人口数量变量（基准年）\n", "population_number = float(input(\"请输入基准年的人口数量：\"))\n", "\n", "# 创建列表保存各年人口数量\n", "population_list = [population_number]\n", "\n", "# 人口数量增长率\n", "population_rate = float(input(\"请输入人口数量增长率(例如0.05表示5%):\"))\n", "\n", "# 预测未来3年的人口数量\n", "population_next_number = population_list[-1] * (1 + population_rate)\n", "population_list.append(population_next_number)\n", "population_next_number = population_list[-1] * (1 + population_rate)\n", "population_list.append(population_next_number)\n", "population_next_number = population_list[-1] * (1 + population_rate)\n", "population_list.append(population_next_number)\n", "print(population_list)\n", "\n", "print('基准年人口数量:{:.2f}人'.format(population_list[0]))\n", "print('第1年人口数量:{:.2f}人'.format(population_list[1]))\n", "print('第2年人口数量:{:.2f}人'.format(population_list[2]))\n", "print('第3年人口数量:{:.2f}人'.format(population_list[3]))"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"ename": "KeyError", "evalue": "'202310137067'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[1], line 54\u001b[0m\n\u001b[0;32m     49\u001b[0m economics_trade \u001b[38;5;241m=\u001b[39m {\n\u001b[0;32m     50\u001b[0m     \u001b[38;5;66;03m# ... 原有字典数据保持不变 ...\u001b[39;00m\n\u001b[0;32m     51\u001b[0m }\n\u001b[0;32m     53\u001b[0m \u001b[38;5;66;03m# 1. 修改张某某的成绩状态为\"缺考\"\u001b[39;00m\n\u001b[1;32m---> 54\u001b[0m economics_trade[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m202310137067\u001b[39m\u001b[38;5;124m\"\u001b[39m][\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m成绩\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m缺考\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m     56\u001b[0m \u001b[38;5;66;03m# 2. 将你的成绩修改为100 (假设你的学号是202210137068)\u001b[39;00m\n\u001b[0;32m     57\u001b[0m \u001b[38;5;66;03m# 请将下面的学号替换为你实际的学号\u001b[39;00m\n\u001b[0;32m     58\u001b[0m your_id \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m202210137068\u001b[39m\u001b[38;5;124m\"\u001b[39m  \n", "\u001b[1;31m<PERSON><PERSON>E<PERSON>r\u001b[0m: '202310137067'"]}], "source": ["economics_trade = {\n", "    \"202210105035\": {\"姓名\": \"朱燕柳\", \"成绩\": 85},\n", "    \"202210105097\": {\"姓名\": \"叶自雅\", \"成绩\": 92},\n", "    \"202210137004\": {\"姓名\": \"罗锦昊\", \"成绩\": 78},\n", "    \"202210137006\": {\"姓名\": \"容燕萍\", \"成绩\": 89},\n", "    \"202210137010\": {\"姓名\": \"倪诗雨\", \"成绩\": 76},\n", "    \"202210137012\": {\"姓名\": \"杨阳\", \"成绩\": 94},\n", "    \"202210137013\": {\"姓名\": \"李冰梅\", \"成绩\": 82},\n", "    \"202210137015\": {\"姓名\": \"陈茵茵\", \"成绩\": 90},\n", "    \"202210137017\": {\"姓名\": \"李素芬\", \"成绩\": 79},\n", "    \"202210137018\": {\"姓名\": \"杨弈昊\", \"成绩\": 87},\n", "    \"202210137019\": {\"姓名\": \"林华鑫\", \"成绩\": 95},\n", "    \"202210137021\": {\"姓名\": \"盘格玲\", \"成绩\": 81},\n", "    \"202210137024\": {\"姓名\": \"李淑菲\", \"成绩\": 93},\n", "    \"202210137026\": {\"姓名\": \"陈慧琦\", \"成绩\": 86},\n", "    \"202210137028\": {\"姓名\": \"陆冠荣\", \"成绩\": 77},\n", "    \"202210137029\": {\"姓名\": \"冯婉怡\", \"成绩\": 91},\n", "    \"202210137030\": {\"姓名\": \"方腾\", \"成绩\": 84},\n", "    \"202210137033\": {\"姓名\": \"王睿\", \"成绩\": 96},\n", "    \"202210137036\": {\"姓名\": \"何昕潞\", \"成绩\": 80},\n", "    \"202210137037\": {\"姓名\": \"陈嘉欣\", \"成绩\": 97},\n", "    \"202210137039\": {\"姓名\": \"洪宁\", \"成绩\": 88},\n", "    \"202210137040\": {\"姓名\": \"莫诗雅\", \"成绩\": 75},\n", "    \"202210137042\": {\"姓名\": \"麦雯静\", \"成绩\": 99},\n", "    \"202210137044\": {\"姓名\": \"郑乐琪\", \"成绩\": 83},\n", "    \"202210137048\": {\"姓名\": \"陈芊卉\", \"成绩\": 98},\n", "    \"202210137049\": {\"姓名\": \"韦雨彤\", \"成绩\": 85},\n", "    \"202210137051\": {\"姓名\": \"刘锦妍\", \"成绩\": 74},\n", "    \"202210137055\": {\"姓名\": \"杨一鸣\", \"成绩\": 90},\n", "    \"202210137056\": {\"姓名\": \"赵富乾\", \"成绩\": 82},\n", "    \"202210137059\": {\"姓名\": \"葛罗明\", \"成绩\": 91},\n", "    \"202210137061\": {\"姓名\": \"张宇森\", \"成绩\": 87},\n", "    \"202210137065\": {\"姓名\": \"赵巧\", \"成绩\": 76},\n", "    \"202210137068\": {\"姓名\": \"廖俊辉\", \"成绩\": 93},\n", "    \"202210137070\": {\"姓名\": \"毛梓航\", \"成绩\": 89},\n", "       \"202310137067\": {\"姓名\": \"张某某\", \"成绩\": 88},\n", "    \"202310137068\": {\"姓名\": \"李某某\", \"成绩\": 92},\n", "}\n", "\n", "# 1. 在字典中修改张某某的成绩状态为”缺考”\n", "# 2. 请将你的成绩修改为100\n", "# 3. 请将李某某姓名及成绩进行删除\n", "# 4. 使用for循环遍历并输出当前字典\n", "# 5. 统计当前总人数并输出\n", "# 6. 从键盘输入input()一个同学的学号,显示该同学的成绩,如字典中无此学号的同学显示”没找到该同学”,使用if/else语句。\n", "# 7. 请统计全班80分以上的同学有多少名？\n", "\n", "\n", "economics_trade = {\n", "    # ... 原有字典数据保持不变 ...\n", "}\n", "\n", "# 1. 修改张某某的成绩状态为\"缺考\"\n", "economics_trade[\"202310137067\"][\"成绩\"] = \"缺考\"\n", "\n", "# 2. 将你的成绩修改为100 (假设你的学号是202210137068)\n", "# 请将下面的学号替换为你实际的学号\n", "your_id = \"202210137068\"  \n", "if your_id in economics_trade:\n", "    economics_trade[your_id][\"成绩\"] = 100\n", "\n", "# 3. 删除李某某的姓名及成绩\n", "del economics_trade[\"202310137068\"]\n", "\n", "# 4. 使用for循环遍历并输出当前字典\n", "print(\"当前所有学生信息：\")\n", "for student_id, info in economics_trade.items():\n", "    print(f\"学号: {student_id}, 姓名: {info['姓名']}, 成绩: {info['成绩']}\")\n", "\n", "# 5. 统计当前总人数并输出\n", "total_students = len(economics_trade)\n", "print(f\"\\n当前总人数: {total_students}人\")\n", "\n", "# 6. 从键盘输入学号查询成绩\n", "search_id = input(\"\\n请输入要查询的学号: \")\n", "if search_id in economics_trade:\n", "    student = economics_trade[search_id]\n", "    print(f\"找到该同学: 姓名: {student['姓名']}, 成绩: {student['成绩']}\")\n", "else:\n", "    print(\"没找到该同学\")\n", "\n", "# 7. 统计全班80分以上的同学数量\n", "high_score_count = 0\n", "for info in economics_trade.values():\n", "    if isinstance(info[\"成绩\"], int) and info[\"成绩\"] >= 80:\n", "        high_score_count += 1\n", "\n", "print(f\"\\n全班80分以上的同学有: {high_score_count}名\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["当前所有学生信息：\n", "学号: 202210105035, 姓名: 朱燕柳, 成绩: 85\n", "学号: 202210105097, 姓名: 叶自雅, 成绩: 92\n", "学号: 202210137004, 姓名: 罗锦昊, 成绩: 78\n", "学号: 202210137006, 姓名: 容燕萍, 成绩: 89\n", "学号: 202210137010, 姓名: 倪诗雨, 成绩: 76\n", "学号: 202210137012, 姓名: 杨阳, 成绩: 94\n", "学号: 202210137013, 姓名: 李冰梅, 成绩: 82\n", "学号: 202210137015, 姓名: 陈茵茵, 成绩: 90\n", "学号: 202210137017, 姓名: 李素芬, 成绩: 79\n", "学号: 202210137018, 姓名: 杨弈昊, 成绩: 87\n", "学号: 202210137019, 姓名: 林华鑫, 成绩: 95\n", "学号: 202210137021, 姓名: 盘格玲, 成绩: 81\n", "学号: 202210137024, 姓名: 李淑菲, 成绩: 93\n", "学号: 202210137026, 姓名: 陈慧琦, 成绩: 86\n", "学号: 202210137028, 姓名: 陆冠荣, 成绩: 77\n", "学号: 202210137029, 姓名: 冯婉怡, 成绩: 91\n", "学号: 202210137030, 姓名: 方腾, 成绩: 84\n", "学号: 202210137033, 姓名: 王睿, 成绩: 96\n", "学号: 202210137036, 姓名: 何昕潞, 成绩: 80\n", "学号: 202210137037, 姓名: 陈嘉欣, 成绩: 97\n", "学号: 202210137039, 姓名: 洪宁, 成绩: 88\n", "学号: 202210137040, 姓名: 莫诗雅, 成绩: 75\n", "学号: 202210137042, 姓名: 麦雯静, 成绩: 99\n", "学号: 202210137044, 姓名: 郑乐琪, 成绩: 83\n", "学号: 202210137048, 姓名: 陈芊卉, 成绩: 98\n", "学号: 202210137049, 姓名: 韦雨彤, 成绩: 85\n", "学号: 202210137051, 姓名: 刘锦妍, 成绩: 74\n", "学号: 202210137055, 姓名: 杨一鸣, 成绩: 90\n", "学号: 202210137056, 姓名: 赵富乾, 成绩: 82\n", "学号: 202210137059, 姓名: 葛罗明, 成绩: 91\n", "学号: 202210137061, 姓名: 张宇森, 成绩: 87\n", "学号: 202210137065, 姓名: 赵巧, 成绩: 76\n", "学号: 202210137068, 姓名: 廖俊辉, 成绩: 100\n", "学号: 202210137070, 姓名: 毛梓航, 成绩: 89\n", "学号: 202310137067, 姓名: 张某某, 成绩: 缺考\n", "\n", "当前总人数: 35人\n", "找到该同学: 姓名: 叶自雅, 成绩: 92\n", "\n", "全班80分以上的同学有: 27名\n"]}], "source": ["economics_trade = {\n", "    \"202210105035\": {\"姓名\": \"朱燕柳\", \"成绩\": 85},\n", "    \"202210105097\": {\"姓名\": \"叶自雅\", \"成绩\": 92},\n", "    \"202210137004\": {\"姓名\": \"罗锦昊\", \"成绩\": 78},\n", "    \"202210137006\": {\"姓名\": \"容燕萍\", \"成绩\": 89},\n", "    \"202210137010\": {\"姓名\": \"倪诗雨\", \"成绩\": 76},\n", "    \"202210137012\": {\"姓名\": \"杨阳\", \"成绩\": 94},\n", "    \"202210137013\": {\"姓名\": \"李冰梅\", \"成绩\": 82},\n", "    \"202210137015\": {\"姓名\": \"陈茵茵\", \"成绩\": 90},\n", "    \"202210137017\": {\"姓名\": \"李素芬\", \"成绩\": 79},\n", "    \"202210137018\": {\"姓名\": \"杨弈昊\", \"成绩\": 87},\n", "    \"202210137019\": {\"姓名\": \"林华鑫\", \"成绩\": 95},\n", "    \"202210137021\": {\"姓名\": \"盘格玲\", \"成绩\": 81},\n", "    \"202210137024\": {\"姓名\": \"李淑菲\", \"成绩\": 93},\n", "    \"202210137026\": {\"姓名\": \"陈慧琦\", \"成绩\": 86},\n", "    \"202210137028\": {\"姓名\": \"陆冠荣\", \"成绩\": 77},\n", "    \"202210137029\": {\"姓名\": \"冯婉怡\", \"成绩\": 91},\n", "    \"202210137030\": {\"姓名\": \"方腾\", \"成绩\": 84},\n", "    \"202210137033\": {\"姓名\": \"王睿\", \"成绩\": 96},\n", "    \"202210137036\": {\"姓名\": \"何昕潞\", \"成绩\": 80},\n", "    \"202210137037\": {\"姓名\": \"陈嘉欣\", \"成绩\": 97},\n", "    \"202210137039\": {\"姓名\": \"洪宁\", \"成绩\": 88},\n", "    \"202210137040\": {\"姓名\": \"莫诗雅\", \"成绩\": 75},\n", "    \"202210137042\": {\"姓名\": \"麦雯静\", \"成绩\": 99},\n", "    \"202210137044\": {\"姓名\": \"郑乐琪\", \"成绩\": 83},\n", "    \"202210137048\": {\"姓名\": \"陈芊卉\", \"成绩\": 98},\n", "    \"202210137049\": {\"姓名\": \"韦雨彤\", \"成绩\": 85},\n", "    \"202210137051\": {\"姓名\": \"刘锦妍\", \"成绩\": 74},\n", "    \"202210137055\": {\"姓名\": \"杨一鸣\", \"成绩\": 90},\n", "    \"202210137056\": {\"姓名\": \"赵富乾\", \"成绩\": 82},\n", "    \"202210137059\": {\"姓名\": \"葛罗明\", \"成绩\": 91},\n", "    \"202210137061\": {\"姓名\": \"张宇森\", \"成绩\": 87},\n", "    \"202210137065\": {\"姓名\": \"赵巧\", \"成绩\": 76},\n", "    \"202210137068\": {\"姓名\": \"廖俊辉\", \"成绩\": 93},\n", "    \"202210137070\": {\"姓名\": \"毛梓航\", \"成绩\": 89},\n", "       \"202310137067\": {\"姓名\": \"张某某\", \"成绩\": 88},\n", "    \"202310137068\": {\"姓名\": \"李某某\", \"成绩\": 92},\n", "}\n", "\n", "# 1. 在字典中修改张某某的成绩状态为”缺考”\n", "# 2. 请将你的成绩修改为100\n", "# 3. 请将李某某姓名及成绩进行删除\n", "# 4. 使用for循环遍历并输出当前字典\n", "# 5. 统计当前总人数并输出\n", "# 6. 从键盘输入input()一个同学的学号,显示该同学的成绩,如字典中无此学号的同学显示”没找到该同学”,使用if/else语句。\n", "# 7. 请统计全班80分以上的同学有多少名？\n", "# 1. 修改张某某的成绩状态为\"缺考\"\n", "economics_trade[\"202310137067\"][\"成绩\"] = \"缺考\"\n", "\n", "# 2. 将你的成绩修改为100 (假设你的学号是202210137068)\n", "your_id = \"202210137068\"  # 请替换为你实际的学号\n", "if your_id in economics_trade:\n", "    economics_trade[your_id][\"成绩\"] = 100\n", "\n", "# 3. 删除李某某的姓名及成绩\n", "if \"202310137068\" in economics_trade:\n", "    del economics_trade[\"202310137068\"]\n", "\n", "# 4. 使用for循环遍历并输出当前字典\n", "print(\"当前所有学生信息：\")\n", "for student_id, info in economics_trade.items():\n", "    print(f\"学号: {student_id}, 姓名: {info['姓名']}, 成绩: {info['成绩']}\")\n", "\n", "# 5. 统计当前总人数并输出\n", "total_students = len(economics_trade)\n", "print(f\"\\n当前总人数: {total_students}人\")\n", "\n", "# 6. 从键盘输入学号查询成绩\n", "search_id = input(\"\\n请输入要查询的学号: \")\n", "if search_id in economics_trade:\n", "    student = economics_trade[search_id]\n", "    print(f\"找到该同学: 姓名: {student['姓名']}, 成绩: {student['成绩']}\")\n", "else:\n", "    print(\"没找到该同学\")\n", "\n", "# 7. 统计全班80分以上的同学数量\n", "high_score_count = 0\n", "for info in economics_trade.values():\n", "    if isinstance(info[\"成绩\"], int) and info[\"成绩\"] >= 80:\n", "        high_score_count += 1\n", "\n", "print(f\"\\n全班80分以上的同学有: {high_score_count}名\")\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 2}