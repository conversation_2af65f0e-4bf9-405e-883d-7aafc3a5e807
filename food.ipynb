pip install baostock
  



import random

# 假设四个饭堂的名称
canteens = [1, 2, 3, 4]

# 初始化一个字典，用于记录每个饭堂去的次数
visits = {canteen: 0 for canteen in canteens}

# 一周七天
for day in range(7):
    # 从尚未去过的饭堂中随机选择一个
    remaining_canteens = [canteen for canteen in canteens if visits[canteen] == 0]
    if remaining_canteens:
        chosen_canteen = random.choice(remaining_canteens)
    else:
        # 如果所有饭堂都去过了，就从所有饭堂中随机选择一个
        chosen_canteen = random.choice(canteens)
    
    # 增加对应饭堂的访问次数
    visits[chosen_canteen] += 1
    
    # 打印当天去的饭堂
    print(f"Day {day+1}: Canteen {chosen_canteen}")

# 打印每个饭堂的访问次数
for canteen, count in visits.items():
    print(f"Canteen {canteen}: {count} times")


a = 10
while 1 <= a<= 10:
    print(a)
    a -= 1

total = 0
num = 1
while num <= 100:
    total += num
    num += 1

print("1到100的和为:", total)

final_money =float(input('复利资金：'))
money_list = [final_money]
rate = 0.05
print(money_list)
a = 0
while a < 5：
    pre_amount = amt_list[-1] *(1+rate)
    amt_list.append(pre_amount)
    a = 1
    print(f'本金为：{final_money},利率为：{rate},第{a}年的本息为：{pre_amount
    }')
compound_interest = final_money * (1 + rate) ** 5 
print(f'使用复利终值公式计算，本金为：{final_money},利率为：{rate},第5年的本息为：{compound_interest}')

a = 1
while a < 6:
    print(f'第{a}年的本息为：{money_lis[a]}')
    a = a + 1

final_money = float(input('复利资金：'))
money_list = [final_money]
rate = 0.05
print(money_list)

a = 0
while a < 5:
    pre_amount = money_list[-1] * (1 + rate)
    money_list.append(pre_amount)
    a += 1
    print(f'本金为：{final_money},利率为：{rate},第{a}年的本息为：{pre_amount}')

compound_interest = final_money * (1 + rate) ** 5 
print(f'使用复利终值公式计算，本金为：{final_money},利率为：{rate},第5年的本息为：{compound_interest}')

a = 1
while a < 6:
    print(f'第{a}年的本息为：{money_list[a]}')
    a = a + 1

total = 0
for num in range(1, 101):
    total += num

print("1到100的和为:", total)   

final_money = float(input('复利资金：'))
money_list = [final_money]
rate = 0.05
print(money_list)

for a in range(1, 6):
    pre_amount = money_list[-1] * (1 + rate)
    money_list.append(pre_amount)
    print(f'本金为：{final_money},利率为：{rate},第{a}年的本息为：{pre_amount}')

compound_interest = final_money * (1 + rate) ** 5 
print(f'使用复利终值公式计算，本金为：{final_money},利率为：{rate},第5年的本息为：{compound_interest}')

# 将第二个while循环也改为for循环
for a in range(1, 6):
    print(f'第{a}年的本息为：{money_list[a]}')

num = int(input("请输入一个整数："))
if num % 2 == 0:
    print(f"{num}是偶数")
else:
    print(f"{num}是奇数")

num = float(input("请输入一个数字："))
if num > 0:
    print("这是一个正数")
elif num < 0:
    print("这是一个负数")
else:
    print("这是零")

total = 0
for num in range(2, 381, 6):
    if num % 4 == 0 and num % 5 == 0:
        total += num
print("满足条件的数字累加和为:", total)

total = 0
num = 2
while num <= 380:
    if num % 4 == 0 and num % 5 == 0:
        total += num
    num += 6
print("满足条件的数字累加和为:", total)

# 定义钞票面值
denominations = [100, 50, 20, 10]
total = 100

# 初始化计数器
count = 0

print("可能的兑换方案：")

# 使用四重循环遍历所有可能的组合
for a in range(total // denominations[0] + 1):  # 100元数量
    for b in range(total // denominations[1] + 1):  # 50元数量
        for c in range(total // denominations[2] + 1):  # 20元数量
            for d in range(total // denominations[3] + 1):  # 10元数量
                # 检查总和是否为100元
                if (a * denominations[0] + b * denominations[1] + 
                    c * denominations[2] + d * denominations[3]) == total:
                    count += 1
                    print(f"方案{count}: {a}张100元, {b}张50元, {c}张20元, {d}张10元")

print(f"\n共有{count}种兑换方案")

a = 0
for i in range(0,2):
    for j in range(0,3):
        for k in range(0,6):
            for l in range(0,11):
                if 100 == 100 * i + 50 * j + 20 * k + 10:
                   a += 1
                   print("第[{}]个兑换方案！".format(a))
                   print("可兑换方案如下：100元：[{}]张，50元：[{}]张，20元：[{}]张，10元：[{}]张".format(i,j,k,l));
                   print("合计：[{}]张".format(i+j+k+l))
                

        print(a)

def is_leap_year(year):
    return (year % 4 == 0 and year % 100 != 0) or (year % 400 == 0)

# 测试示例
test_years = [2000, 2004, 2100, 2023]
for year in test_years:
    if is_leap_year(year):
        print(f"{year} 是闰年")
    else:
        print(f"{year} 不是闰年")

def is_leap_year(year):
    return (year % 4 == 0 and year % 100 != 0) or (year % 400 == 0)

# 获取用户输入
try:
    input_year = int(input("请输入一个年份: "))
    if is_leap_year(input_year):
        print(f"{input_year} 是闰年")
    else:
        print(f"{input_year} 不是闰年")
except ValueError:
    print("输入无效，请输入一个有效的整数年份。")

def calculate_tax(income):
    if 0 < income <= 36000:
        return income * 0.03
    elif 36000 < income <= 144000:
        return income * 0.10 - 2520
    elif 144000 < income <= 420000:
        return income * 0.20 - 16920
    elif 420000 < income <= 660000:
        return income * 0.30 - 52920
    elif 660000 < income <= 960000:
        return income * 0.35 - 85920
    elif income > 960000:
        return income * 0.45 - 181920
    else:
        return 0




     

income = float(input("请输入全年综合所得收入： (以元为单位)")) -60000
tax = calculate_tax(income)
print("全年总收入：",income + 60000)
print("应纳税额：",tax)
print("税后收入为：",income + 60000 - tax)

income = float(input("请输入全年综合所得收入： (以元为单位)")) - 60000
if income <= 0:
    tax = 0
elif income <= 36000:
    tax = income * 0.03
elif income <= 144000:
    tax = income * 0.10 - 2520
elif income <= 420000:
    tax = income * 0.20 - 16920
elif income <= 660000:
    tax = income * 0.30 - 52920
elif income <= 960000:
    tax = income * 0.35 - 85920
else:
    tax = income * 0.45 - 181920

print("全年总收入：", income + 60000)
print("应纳税额：", tax)
print("税后收入为：", income + 60000 - tax)

import numpy as np
close_price = np.array([[3.22, 3.21, 3.20, 3.19, 3.20],    #中国银行          
                        [3.19, 3.16, 3.17, 3.15, 3.15],    #农业银行                                
                        [4.96, 4.93, 4.92, 4.90, 4.89],    #工商银行           
                        [6.21, 6.19, 6.13, 6.17, 6.20]])  #建设银行
close_price = [0][1] 
close_price = [0,1]



#【实操作业】编写程序
   
#某公司销售部门记录了连续5天里每天的销售量，数据被保存在一个5x5的矩阵中，其中每一行代表一天的销售数据，每一列代表不同的产品销售量。
#现在需要对这些销售数据进行一系列的分析和处理。

#1、数据准备：首先，使用Numpy创建一个5x5的随机整数矩阵，代表5天（行）里5种不同产品（列）的销售量。矩阵中的元素值应在10到100之间。
# 提示：使用np.random.randint()函数生成随机整数矩阵。

# 数据准备：
import numpy as np
# 创建一个5x5的随机整数矩阵，代表5天（行）里5种不同产品（列）的销售量。
sales_data = np.random.randint(10, 101, size=(5, 5))
print(sales_data)

#2、销售总额计算：计算这5天的总销售额（即将矩阵中的所有元素相加）。
# 销售总额计算：
total_sales = np.sum(sales_data)
print("总销售额：", total_sales)

#3、日均销售额：计算每种产品的日均销售量，即矩阵每一列的平均值。
# 日均销售额：100
daily_average_sales = np.mean(sales_data, axis=0)
print("日均销售额：", daily_average_sales)

#4、找出畅销产品：找出日均销售量最高的产品及其销售量。
# 找出畅销产品：
max_sales_index = np.argmax(daily_average_sales)
max_sales_product = max_sales_index + 1  # 产品编号从1开始
max_sales = daily_average_sales[max_sales_index]
print("畅销产品编号：", max_sales_product)
print("畅销产品日均销售量：", max_sales)

#5、销售波动分析：计算每种产品销售量的标准差，以评估其销售波动性。
# 销售波动分析：
sales_variability = np.std(sales_data, axis=0)
print("销售波动性：", sales_variability)

#6、商品日均销售数据的可视化处理
# 商品日均销售数据的可视化处理：
import matplotlib.pyplot as plt
# 绘制柱状图
plt.bar(range(1, 6), daily_average_sales)  # 横轴为产品编号，纵轴为日均销售量
plt.xlabel("产品编号")
import numpy as np  
 
sales_name = np.array(["钢笔", "铅笔", "彩笔", "中性笔", "毛笔"])  
sales_data = np.random.randint(10, 101, size=(5, 5))  
print(sales_data)  

# 由于随机性，每运行一次获得的数据都不一样，可以通过设置随机种子，保证数据可复现：  np.random.seed(0）



# 由于随机性，每运行一次获得的数据都不一样，可以通过设置随机种子，保证数据可复现：  np.random.seed(0）

#【实操作业】编写程序
    
#某公司销售部门记录了连续5天里每天的销售量，数据被保存在一个5x5的矩阵中，其中每一行代表一天的销售数据，每一列代表不同的产品销售量。
#现在需要对这些销售数据进行一系列的分析和处理。

# 设置随机种子以保证数据可复现
import numpy as np
np.random.seed(0)

# 1、数据准备：首先，使用Numpy创建一个5x5的随机整数矩阵，代表5天（行）里5种不同产品（列）的销售量。矩阵中的元素值应在10到100之间。
sales_name = np.array(["钢笔", "铅笔", "彩笔", "中性笔", "毛笔"])  
sales_data = np.random.randint(10, 101, size=(5, 5))  
print(sales_data)

#2、销售总额计算：计算这5天的总销售额（即将矩阵中的所有元素相加）。
total_sales = np.sum(sales_data)
print("总销售额：", total_sales)

#3、日均销售额：计算每种产品的日均销售量，即矩阵每一列的平均值。
daily_average_sales = np.mean(sales_data, axis=0)
print("日均销售额：", daily_average_sales)

#4、找出畅销产品：找出日均销售量最高的产品及其销售量。
max_sales_index = np.argmax(daily_average_sales)
max_sales_product = sales_name[max_sales_index]
max_sales = daily_average_sales[max_sales_index]
print("畅销产品名称：", max_sales_product)
print("畅销产品日均销售量：", max_sales)

#5、销售波动分析：计算每种产品销售量的标准差，以评估其销售波动性。
sales_variability = np.std(sales_data, axis=0)
print("销售波动性：", sales_variability)

#6、商品日均销售数据的可视化处理
# ... existing code ...

#6、商品日均销售数据的可视化处理
import matplotlib.pyplot as plt
# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
# 解决负号显示问题
plt.rcParams['axes.unicode_minus'] = False

# 修改颜色为马卡龙色
colors = ['#FF7F50', '#FF6347', '#FF4500', '#FFD700', '#FFA07A']    
# 绘制柱状图
plt.figure()
plt.bar(sales_name, daily_average_sales, color=colors)  



# 绘制柱状图
plt.figure()
plt.bar(sales_name, daily_average_sales, color=colors)
plt.xlabel("产品名称")
plt.ylabel("日均销售量")
plt.title("商品日均销售数据（柱状图）")

# 绘制饼状图
plt.figure()
plt.pie(daily_average_sales, labels=sales_name, colors=colors, autopct='%1.1f%%', startangle=90)
plt.axis('equal')  
plt.title("商品日均销售数据占比（饼状图）")

# 绘制折线图
plt.figure()
plt.plot(sales_name, daily_average_sales, marker='o', color='blue')
plt.xlabel("产品名称")
plt.ylabel("日均销售量")
plt.title("商品日均销售数据（折线图）")
plt.grid(True)

plt.show()

# ... existing code ...

# ... existing code ...

# ... existing code ...
# ... existing code ...