# 学校饭堂随机安排程序（简洁版）
# 避免输出截断，精简显示内容

import random

def arrange_canteens():
    """
    为一周7天随机安排4个饭堂用餐
    确保每个饭堂至少被安排一次
    """
    # 定义饭堂和星期
    canteens = ['第一饭堂', '第二饭堂', '第三饭堂', '第四饭堂']
    weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
    
    # 确保每个饭堂至少被安排一次
    arrangement = canteens.copy()  # 前4天保证覆盖所有饭堂
    random.shuffle(arrangement)    # 随机打乱顺序
    
    # 为剩余3天随机分配饭堂
    for _ in range(3):
        arrangement.append(random.choice(canteens))
    
    # 整体随机打乱
    random.shuffle(arrangement)
    
    return weekdays, arrangement

# 生成饭堂安排
weekdays, canteen_arrangement = arrange_canteens()

# 验证每个饭堂是否都被安排
all_canteens = ['第一饭堂', '第二饭堂', '第三饭堂', '第四饭堂']
is_valid = all(canteen in canteen_arrangement for canteen in all_canteens)

print("=== 学校饭堂随机安排结果 ===")
print()

# 输出一周安排
for day, canteen in zip(weekdays, canteen_arrangement):
    print(f"{day}：{canteen}")

print()

# 统计每个饭堂安排次数
canteen_count = {}
for canteen in canteen_arrangement:
    canteen_count[canteen] = canteen_count.get(canteen, 0) + 1

print("饭堂安排统计：")
for canteen in all_canteens:
    count = canteen_count.get(canteen, 0)
    print(f"{canteen}：{count}次")

print()
print(f"验证结果：{'✅ 每个饭堂都至少安排一次' if is_valid else '❌ 存在未安排的饭堂'}")
print(f"不同饭堂数：{len(set(canteen_arrangement))}个")

# 生成3个额外方案进行对比
print("\n其他随机方案：")
for i in range(3):
    _, arr = arrange_canteens()
    valid = all(c in arr for c in all_canteens)
    print(f"方案{i+1}：{' '.join([c[-3:] for c in arr])} {'✅' if valid else '❌'}")

print("\n程序完成！每次运行结果都不同。")