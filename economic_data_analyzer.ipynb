{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 全球主要国家经济数据分析\n", "\n", "使用pandas和matplotlib分析全球主要国家的经济数据，包括GDP、人口、失业率等指标。\n", "\n", "**功能：数据读取、处理、分析、可视化**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 全球主要国家经济数据分析程序\n", "# 使用pandas和matplotlib进行数据分析和可视化\n", "\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import matplotlib\n", "\n", "# 设置中文字体支持，解决中文乱码问题\n", "matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']\n", "matplotlib.rcParams['axes.unicode_minus'] = False\n", "\n", "print(\"=== 全球主要国家经济数据分析 ===\")\n", "print(\"分析GDP、人口、失业率等经济指标\")\n", "print(\"-\" * 50)\n", "\n", "# 首先创建示例数据文件（模拟mvsData.xlsx）\n", "print(\"正在创建示例数据文件...\")\n", "\n", "# 创建20个国家的经济数据\n", "sample_data = {\n", "    '国家': ['美国', '中国', '日本', '德国', '印度', '英国', '法国', '意大利', '巴西', '加拿大',\n", "            '俄罗斯', '韩国', '西班牙', '澳大利亚', '墨西哥', '印度尼西亚', '荷兰', '沙特阿拉伯', '土耳其', '瑞士'],\n", "    '国内生产总值（十亿美元）': [21427.7, 14342.6, 4937.4, 3846.4, 2875.1, 2829.1, 2715.5, 2001.2, 1608.9, 1736.4,\n", "                      1483.5, 1810.0, 1394.1, 1392.7, 1078.2, 1058.4, 909.0, 700.1, 761.4, 752.2],\n", "    '人口（百万）': [331.0, 1439.3, 126.5, 83.8, 1380.0, 67.9, 65.3, 60.5, 212.6, 38.0,\n", "                  145.9, 51.3, 46.8, 25.5, 128.9, 273.5, 17.1, 34.8, 84.3, 8.7],\n", "    '失业率（%）': [3.7, 3.6, 2.8, 3.2, 2.6, 3.8, 7.9, 9.9, 11.9, 5.7,\n", "                 4.6, 3.8, 14.1, 5.2, 3.5, 5.3, 3.4, 5.6, 13.7, 2.3]\n", "}\n", "\n", "# 创建DataFrame并保存到Excel文件\n", "df_sample = pd.DataFrame(sample_data)\n", "with pd.ExcelWriter('mvsData.xlsx', engine='openpyxl') as writer:\n", "    df_sample.to_excel(writer, sheet_name='d1', index=False)\n", "\n", "print(\"✅ 示例数据文件已创建\")\n", "\n", "# (1) 数据读取与初步检查\n", "print(\"\\n=== (1) 数据读取与初步检查 ===\")\n", "\n", "# ① 使用Pandas读取Excel文件，指定引擎为openpyxl\n", "df = pd.read_excel('mvsData.xlsx', sheet_name='d1', engine='openpyxl')\n", "print(\"✅ 数据读取成功\")\n", "\n", "# ② 重置索引为1~20\n", "df.index = range(1, len(df) + 1)\n", "print(\"✅ 索引已重置为1~20\")\n", "\n", "# ③ 显示前21行数据（验证是否包含全部数据）\n", "print(f\"\\n数据形状：{df.shape}\")\n", "print(\"\\n前21行数据（实际只有20行）：\")\n", "print(df.head(21))\n", "\n", "# (2) 数据选择与提取\n", "print(\"\\n=== (2) 数据选择与提取 ===\")\n", "\n", "# ① 单列提取：提取GDP列\n", "gdp_data = df['国内生产总值（十亿美元）']\n", "print(\"\\n① 所有国家的GDP数据：\")\n", "print(gdp_data)\n", "\n", "# ② 多列提取：提取国家和失业率\n", "country_unemployment = df[['国家', '失业率（%）']]\n", "print(\"\\n② 国家和失业率数据：\")\n", "print(country_unemployment)\n", "\n", "# (3) 条件筛选\n", "print(\"\\n=== (3) 条件筛选 ===\")\n", "\n", "# 筛选失业率低于4%的国家\n", "low_unemployment = df[df['失业率（%）'] < 4][['国家', '失业率（%）']]\n", "print(\"\\n失业率低于4%的国家：\")\n", "print(low_unemployment)\n", "\n", "# (4) 计算新列\n", "print(\"\\n=== (4) 计算新列 ===\")\n", "\n", "# ① 计算人均GDP：GDP(十亿美元) × 10^9 / 人口(百万) × 10^6\n", "df['人均GDP（美元）'] = (df['国内生产总值（十亿美元）'] * 1e9) / (df['人口（百万）'] * 1e6)\n", "\n", "# ② 输出前5名国家的人均GDP\n", "top5_per_capita = df.nlargest(5, '人均GDP（美元）')[['国家', '人均GDP（美元）']]\n", "print(\"\\n人均GDP前5名国家：\")\n", "print(top5_per_capita)\n", "\n", "# (5) 数据排序\n", "print(\"\\n=== (5) 数据排序 ===\")\n", "\n", "# 按GDP从高到低排序，输出前5名\n", "top5_gdp = df.nlargest(5, '国内生产总值（十亿美元）')[['国家', '国内生产总值（十亿美元）']]\n", "print(\"\\nGDP前5名国家：\")\n", "print(top5_gdp)\n", "\n", "# (6) 数据质量检查\n", "print(\"\\n=== (6) 数据质量检查 ===\")\n", "\n", "# ① 统计缺失值\n", "missing_values = df.isnull().sum()\n", "print(\"\\n① 各列缺失值数量：\")\n", "print(missing_values)\n", "\n", "# ② 统计重复值\n", "duplicate_count = df.duplicated().sum()\n", "print(f\"\\n② 重复值数量：{duplicate_count}\")\n", "\n", "# (7) 统计分析\n", "print(\"\\n=== (7) 统计分析 ===\")\n", "\n", "# ① 描述性统计\n", "numeric_columns = ['国内生产总值（十亿美元）', '人口（百万）', '失业率（%）', '人均GDP（美元）']\n", "desc_stats = df[numeric_columns].describe()\n", "print(\"\\n① 数值列描述性统计：\")\n", "print(desc_stats)\n", "\n", "# ② 相关系数矩阵（映射为英文列名）\n", "df_corr = df[numeric_columns].copy()\n", "df_corr.columns = ['GDP', 'Population', 'Unemployment', 'GDP_per_capita']\n", "correlation_matrix = df_corr.corr()\n", "print(\"\\n② 相关系数矩阵：\")\n", "print(correlation_matrix)\n", "\n", "# (8) 数据可视化\n", "print(\"\\n=== (8) 数据可视化 ===\")\n", "print(\"正在生成图表...\")\n", "\n", "# 创建2行2列的子图，画布大小15x10英寸\n", "fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))\n", "\n", "# 子图1：GDP前10国家条形图\n", "top10_gdp = df.nlargest(10, '国内生产总值（十亿美元）')\n", "ax1.bar(top10_gdp['国家'], top10_gdp['国内生产总值（十亿美元）'], \n", "        color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', \n", "               '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'])\n", "ax1.set_title('GDP Top10国家', fontsize=14, fontweight='bold')\n", "ax1.set_ylabel('GDP（十亿美元）', fontsize=12)\n", "ax1.tick_params(axis='x', rotation=45)\n", "ax1.grid(True, alpha=0.3)\n", "\n", "# 子图2：各国失业率分布折线图\n", "ax2.plot(df['国家'], df['失业率（%）'], linestyle='--', marker='o', \n", "         linewidth=2, markersize=6, color='#E74C3C')\n", "ax2.set_title('各国失业率分布', fontsize=14, fontweight='bold')\n", "ax2.set_ylabel('失业率（%）', fontsize=12)\n", "ax2.tick_params(axis='x', rotation=90)\n", "ax2.grid(True, alpha=0.3)\n", "\n", "# 子图3：GDP与失业率关系散点图\n", "ax3.scatter(df['国内生产总值（十亿美元）'], df['失业率（%）'], \n", "           s=100, alpha=0.7, c='#3498DB', edgecolors='black')\n", "ax3.set_title('GDP与失业率关系', fontsize=14, fontweight='bold')\n", "ax3.set_xlabel('GDP（十亿美元）', fontsize=12)\n", "ax3.set_ylabel('失业率（%）', fontsize=12)\n", "ax3.grid(True, alpha=0.3)\n", "\n", "# 子图4：人均GDP前10国家（额外图表）\n", "top10_per_capita = df.nlargest(10, '人均GDP（美元）')\n", "ax4.barh(top10_per_capita['国家'], top10_per_capita['人均GDP（美元）'], \n", "         color='#2ECC71')\n", "ax4.set_title('人均GDP Top10国家', fontsize=14, fontweight='bold')\n", "ax4.set_xlabel('人均GDP（美元）', fontsize=12)\n", "ax4.grid(True, alpha=0.3)\n", "\n", "# 自动调整子图间距\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"✅ 图表生成完成\")\n", "\n", "# 输出最终汇总报告\n", "print(\"\\n=== 分析汇总报告 ===\")\n", "print(f\"数据集包含 {len(df)} 个国家的经济数据\")\n", "print(f\"GDP最高国家：{df.loc[df['国内生产总值（十亿美元）'].idxmax(), '国家']}\")\n", "print(f\"人均GDP最高国家：{df.loc[df['人均GDP（美元）'].idxmax(), '国家']}\")\n", "print(f\"失业率最低国家：{df.loc[df['失业率（%）'].idxmin(), '国家']}\")\n", "print(f\"失业率最高国家：{df.loc[df['失业率（%）'].idxmax(), '国家']}\")\n", "print(f\"平均GDP：{df['国内生产总值（十亿美元）'].mean():.2f} 十亿美元\")\n", "print(f\"平均失业率：{df['失业率（%）'].mean():.2f}%\")\n", "\n", "print(\"\\n程序执行完成！\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 程序说明\n", "\n", "### 使用的库：\n", "- **pandas**：数据读取、处理、分析\n", "- **matplotlib**：数据可视化\n", "- **numpy**：数值计算支持\n", "\n", "### 完成的任务：\n", "1. **数据读取**：从Excel文件读取经济数据\n", "2. **数据处理**：索引重置、列提取、条件筛选\n", "3. **数据计算**：人均GDP计算、排序分析\n", "4. **质量检查**：缺失值、重复值统计\n", "5. **统计分析**：描述性统计、相关性分析\n", "6. **数据可视化**：多种图表展示\n", "\n", "### 核心功能：\n", "- **数据读取**：`pd.read_excel()` 读取Excel文件\n", "- **数据筛选**：条件筛选和多列提取\n", "- **数据计算**：人均GDP公式计算\n", "- **统计分析**：`describe()`, `corr()` 统计函数\n", "- **可视化**：条形图、折线图、散点图\n", "\n", "### 图表说明：\n", "1. **GDP Top10条形图**：显示GDP最高的10个国家\n", "2. **失业率分布折线图**：展示各国失业率变化\n", "3. **GDP与失业率散点图**：分析两者关系\n", "4. **人均GDP Top10水平条形图**：显示人均GDP最高国家\n", "\n", "### 程序特点：\n", "- ✅ 完整的8个分析任务\n", "- ✅ 中文字体支持，解决乱码问题\n", "- ✅ 自动创建示例数据文件\n", "- ✅ 详细的统计分析和可视化\n", "- ✅ 完整的代码注释\n", "- ✅ 汇总报告输出"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}