{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 订货量应付货款计算器\n", "\n", "本程序用于根据不同订货量计算应付货款，根据订货量大小给予不同的折扣。\n", "\n", "## 折扣规则：\n", "- 订货量 < 300：无折扣（0%）\n", "- 订货量 300-499：折扣 3%\n", "- 订货量 500-999：折扣 5%\n", "- 订货量 1000-1999：折扣 8%\n", "- 订货量 ≥ 2000：折扣 10%"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 订货量应付货款计算程序\n", "# 功能：根据不同订货量计算应付货款，实现阶梯式折扣\n", "# 计算公式：应付货款 = 订货量 × 价格 × (1 - 折扣)\n", "\n", "print(\"=== 订货量应付货款计算器 ===\")\n", "print()\n", "print(\"折扣规则：\")\n", "print(\"• 订货量 < 300：无折扣（0%）\")\n", "print(\"• 订货量 300-499：折扣 3%\")\n", "print(\"• 订货量 500-999：折扣 5%\")\n", "print(\"• 订货量 1000-1999：折扣 8%\")\n", "print(\"• 订货量 ≥ 2000：折扣 10%\")\n", "print()\n", "\n", "# (1) 使用input函数分别输入标准价格和订货量\n", "print(\"请输入订货信息：\")\n", "\n", "# 输入标准价格（单价）\n", "standard_price = float(input(\"请输入标准价格（元/件）：\"))\n", "\n", "# 输入订货量\n", "order_quantity = int(input(\"请输入订货量（件）：\"))\n", "\n", "print()\n", "print(f\"标准价格：{standard_price:.2f} 元/件\")\n", "print(f\"订货量：{order_quantity} 件\")\n", "print()\n", "\n", "# (2) 根据订货量大小，使用多分支if/elif/else语句计算折扣\n", "print(\"正在计算折扣...\")\n", "\n", "# 初始化折扣率变量\n", "discount_rate = 0.0  # 折扣率（小数形式）\n", "discount_description = \"\"  # 折扣描述\n", "\n", "# 使用多分支if/elif/else语句判断订货量并设置相应折扣\n", "if order_quantity < 300:\n", "    # 订货量300以下，没有折扣\n", "    discount_rate = 0.0\n", "    discount_description = \"无折扣\"\n", "    print(f\"订货量 {order_quantity} 件 < 300 件，{discount_description}\")\n", "    \n", "elif order_quantity >= 300 and order_quantity < 500:\n", "    # 订货量300及以上，500以下，折扣为3%\n", "    discount_rate = 0.03\n", "    discount_description = \"3% 折扣\"\n", "    print(f\"订货量 {order_quantity} 件，300-499 件区间，享受 {discount_description}\")\n", "    \n", "elif order_quantity >= 500 and order_quantity < 1000:\n", "    # 订货量500及以上，1000以下，折扣5%\n", "    discount_rate = 0.05\n", "    discount_description = \"5% 折扣\"\n", "    print(f\"订货量 {order_quantity} 件，500-999 件区间，享受 {discount_description}\")\n", "    \n", "elif order_quantity >= 1000 and order_quantity < 2000:\n", "    # 订货量1000及以上，2000以下，折扣8%\n", "    discount_rate = 0.08\n", "    discount_description = \"8% 折扣\"\n", "    print(f\"订货量 {order_quantity} 件，1000-1999 件区间，享受 {discount_description}\")\n", "    \n", "else:\n", "    # 订货量2000及以上，折扣10%\n", "    discount_rate = 0.10\n", "    discount_description = \"10% 折扣\"\n", "    print(f\"订货量 {order_quantity} 件 ≥ 2000 件，享受 {discount_description}\")\n", "\n", "print()\n", "\n", "# 计算应付货款\n", "# 应付货款 = 订货量 × 价格 × (1 - 折扣)\n", "total_amount_before_discount = order_quantity * standard_price  # 折扣前总金额\n", "discount_amount = total_amount_before_discount * discount_rate  # 折扣金额\n", "payable_amount = order_quantity * standard_price * (1 - discount_rate)  # 应付货款\n", "\n", "print(\"计算过程：\")\n", "print(f\"折扣前总金额 = {order_quantity} × {standard_price:.2f} = {total_amount_before_discount:.2f} 元\")\n", "print(f\"折扣金额 = {total_amount_before_discount:.2f} × {discount_rate:.2%} = {discount_amount:.2f} 元\")\n", "print(f\"应付货款 = {total_amount_before_discount:.2f} × (1 - {discount_rate:.2%}) = {payable_amount:.2f} 元\")\n", "print()\n", "\n", "# (3) 使用f语句或format()格式化方法及print()函数输出相应的货款\n", "print(\"=== 订货量应付货款计算结果 ===\")\n", "print()\n", "\n", "# 使用f字符串格式化输出\n", "print(f\"标准价格：{standard_price:.2f} 元/件\")\n", "print(f\"订货量：{order_quantity:,} 件\")\n", "print(f\"适用折扣：{discount_description}\")\n", "print(f\"折扣率：{discount_rate:.2%}\")\n", "print()\n", "\n", "# 使用format()方法格式化输出\n", "print(\"折扣前总金额：{:,.2f} 元\".format(total_amount_before_discount))\n", "print(\"折扣金额：{:,.2f} 元\".format(discount_amount))\n", "print(\"应付货款：{:,.2f} 元\".format(payable_amount))\n", "print()\n", "\n", "# 计算节省金额\n", "savings = discount_amount\n", "if savings > 0:\n", "    print(f\"恭喜！您节省了：{savings:,.2f} 元\")\n", "    savings_percentage = (savings / total_amount_before_discount) * 100\n", "    print(f\"节省比例：{savings_percentage:.2f}%\")\n", "else:\n", "    print(\"本次订货无折扣优惠\")\n", "\n", "print()\n", "print(\"计算完成！感谢您的订货！\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 程序说明\n", "\n", "### 计算公式：\n", "**应付货款 = 订货量 × 标准价格 × (1 - 折扣率)**\n", "\n", "### 折扣阶梯：\n", "| 订货量范围 | 折扣率 | 说明 |\n", "|-----------|--------|------|\n", "| < 300 件 | 0% | 无折扣 |\n", "| 300-499 件 | 3% | 小批量折扣 |\n", "| 500-999 件 | 5% | 中批量折扣 |\n", "| 1000-1999 件 | 8% | 大批量折扣 |\n", "| ≥ 2000 件 | 10% | 超大批量折扣 |\n", "\n", "### 程序特点：\n", "- ✅ 使用 `input()` 函数获取标准价格和订货量\n", "- ✅ 使用多分支 `if/elif/else` 语句实现折扣判断\n", "- ✅ 使用 `f` 字符串和 `format()` 方法格式化输出\n", "- ✅ 详细的计算过程展示\n", "- ✅ 完整的中文注释\n", "- ✅ 千分符格式化显示金额\n", "- ✅ 计算并显示节省金额\n", "\n", "### 示例计算：\n", "**输入：**\n", "- 标准价格：100.00 元/件\n", "- 订货量：1500 件\n", "\n", "**输出：**\n", "- 适用折扣：8% 折扣\n", "- 折扣前总金额：150,000.00 元\n", "- 折扣金额：12,000.00 元\n", "- 应付货款：138,000.00 元\n", "- 节省金额：12,000.00 元"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}