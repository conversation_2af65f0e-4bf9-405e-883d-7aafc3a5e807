
# (1) 使用input函数分别输入标准价格和订货量
print("请输入订货信息：")
# 输入标准价格（单价）
standard_price = float(input("请输入标准价格（元/件）："))
# 输入订货量
order_quantity = int(input("请输入订货量（件）："))
print(f"标准价格：{standard_price:.2f} 元/件")
print(f"订货量：{order_quantity} 件")
# (2) 根据订货量大小，使用多分支if/elif/else语句计算折扣
print("正在计算折扣...")
# 初始化折扣率变量
discount_rate = 0.0  # 折扣率（小数形式）
discount_description = ""  # 折扣描述
# 使用多分支if/elif/else语句判断订货量并设置相应折扣
if order_quantity < 300:
    # 订货量300以下，没有折扣
    discount_rate = 0.0
    discount_description = "无折扣"
    print(f"订货量 {order_quantity} 件 < 300 件，{discount_description}")   
elif order_quantity >= 300 and order_quantity < 500:
    # 订货量300及以上，500以下，折扣为3%
    discount_rate = 0.03
    discount_description = "3% 折扣"
    print(f"订货量 {order_quantity} 件，300-499 件区间，享受 {discount_description}")  
elif order_quantity >= 500 and order_quantity < 1000:
    # 订货量500及以上，1000以下，折扣5%
    discount_rate = 0.05
    discount_description = "5% 折扣"
    print(f"订货量 {order_quantity} 件，500-999 件区间，享受 {discount_description}")    
elif order_quantity >= 1000 and order_quantity < 2000:
    # 订货量1000及以上，2000以下，折扣8%
    discount_rate = 0.08
    discount_description = "8% 折扣"
    print(f"订货量 {order_quantity} 件，1000-1999 件区间，享受 {discount_description}")  
else:
    # 订货量2000及以上，折扣10%
    discount_rate = 0.10
    discount_description = "10% 折扣"
    print(f"订货量 {order_quantity} 件 ≥ 2000 件，享受 {discount_description}")
# 计算应付货款
# 应付货款 = 订货量 × 价格 × (1 - 折扣)
total_amount_before_discount = order_quantity * standard_price  # 折扣前总金额
discount_amount = total_amount_before_discount * discount_rate  # 折扣金额
payable_amount = order_quantity * standard_price * (1 - discount_rate)  # 应付货款
print("计算过程：")
print(f"折扣前总金额 = {order_quantity} × {standard_price:.2f} = {total_amount_before_discount:.2f} 元")
print(f"折扣金额 = {total_amount_before_discount:.2f} × {discount_rate:.2%} = {discount_amount:.2f} 元")
print(f"应付货款 = {total_amount_before_discount:.2f} × (1 - {discount_rate:.2%}) = {payable_amount:.2f} 元")
print()
# (3) 使用f语句或format()格式化方法及print()函数输出相应的货款
print("=== 订货量应付货款计算结果 ===")
# 使用f字符串格式化输出
print(f"标准价格：{standard_price:.2f} 元/件")
print(f"订货量：{order_quantity:,} 件")
print(f"适用折扣：{discount_description}")
print(f"折扣率：{discount_rate:.2%}")
# 使用format()方法格式化输出
print("折扣前总金额：{:,.2f} 元".format(total_amount_before_discount))
print("折扣金额：{:,.2f} 元".format(discount_amount))
print("应付货款：{:,.2f} 元".format(payable_amount))
# 计算节省金额
savings = discount_amount
if savings > 0:
    print(f"恭喜！您节省了：{savings:,.2f} 元")
    savings_percentage = (savings / total_amount_before_discount) * 100
    print(f"节省比例：{savings_percentage:.2f}%")
else:
    print("本次订货无折扣优惠")

print()
print("计算完成！感谢您的订货！")