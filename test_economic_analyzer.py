# 验证经济数据分析程序
import pandas as pd
import numpy as np

def test_economic_analyzer():
    print("=== 经济数据分析程序验证 ===")
    
    # 创建示例数据
    sample_data = {
        '国家': ['美国', '中国', '日本', '德国', '印度', '英国', '法国', '意大利', '巴西', '加拿大',
                '俄罗斯', '韩国', '西班牙', '澳大利亚', '墨西哥', '印度尼西亚', '荷兰', '沙特阿拉伯', '土耳其', '瑞士'],
        '国内生产总值（十亿美元）': [21427.7, 14342.6, 4937.4, 3846.4, 2875.1, 2829.1, 2715.5, 2001.2, 1608.9, 1736.4,
                          1483.5, 1810.0, 1394.1, 1392.7, 1078.2, 1058.4, 909.0, 700.1, 761.4, 752.2],
        '人口（百万）': [331.0, 1439.3, 126.5, 83.8, 1380.0, 67.9, 65.3, 60.5, 212.6, 38.0,
                      145.9, 51.3, 46.8, 25.5, 128.9, 273.5, 17.1, 34.8, 84.3, 8.7],
        '失业率（%）': [3.7, 3.6, 2.8, 3.2, 2.6, 3.8, 7.9, 9.9, 11.9, 5.7,
                     4.6, 3.8, 14.1, 5.2, 3.5, 5.3, 3.4, 5.6, 13.7, 2.3]
    }
    
    # 创建DataFrame
    df = pd.DataFrame(sample_data)
    df.index = range(1, len(df) + 1)
    
    print(f"数据形状：{df.shape}")
    print("✅ 数据创建成功")
    
    # (1) 验证数据读取
    print("\\n=== (1) 数据读取验证 ===")
    print(f"数据行数：{len(df)} 行")
    print(f"数据列数：{len(df.columns)} 列")
    print(f"索引范围：{df.index.min()} - {df.index.max()}")
    
    # (2) 验证数据提取
    print("\\n=== (2) 数据提取验证 ===")
    gdp_data = df['国内生产总值（十亿美元）']
    print(f"GDP数据长度：{len(gdp_data)}")
    print(f"GDP最大值：{gdp_data.max():.2f}")
    
    country_unemployment = df[['国家', '失业率（%）']]
    print(f"国家-失业率数据形状：{country_unemployment.shape}")
    
    # (3) 验证条件筛选
    print("\\n=== (3) 条件筛选验证 ===")
    low_unemployment = df[df['失业率（%）'] < 4]
    print(f"失业率低于4%的国家数量：{len(low_unemployment)}")
    print("低失业率国家：", low_unemployment['国家'].tolist())
    
    # (4) 验证人均GDP计算
    print("\\n=== (4) 人均GDP计算验证 ===")
    df['人均GDP（美元）'] = (df['国内生产总值（十亿美元）'] * 1e9) / (df['人口（百万）'] * 1e6)
    
    # 手动验证美国的人均GDP
    us_gdp = 21427.7 * 1e9
    us_population = 331.0 * 1e6
    us_per_capita_manual = us_gdp / us_population
    us_per_capita_calc = df[df['国家'] == '美国']['人均GDP（美元）'].iloc[0]
    
    print(f"美国人均GDP（手动计算）：{us_per_capita_manual:.2f}")
    print(f"美国人均GDP（程序计算）：{us_per_capita_calc:.2f}")
    print(f"计算验证：{'✅' if abs(us_per_capita_manual - us_per_capita_calc) < 1 else '❌'}")
    
    top5_per_capita = df.nlargest(5, '人均GDP（美元）')
    print(f"\\n人均GDP前5名：")
    for i, row in top5_per_capita.iterrows():
        print(f"  {row['国家']}：{row['人均GDP（美元）']:,.2f} 美元")
    
    # (5) 验证排序
    print("\\n=== (5) 排序验证 ===")
    top5_gdp = df.nlargest(5, '国内生产总值（十亿美元）')
    print("GDP前5名国家：")
    for i, row in top5_gdp.iterrows():
        print(f"  {row['国家']}：{row['国内生产总值（十亿美元）']:,.2f} 十亿美元")
    
    # (6) 验证数据质量
    print("\\n=== (6) 数据质量验证 ===")
    missing_values = df.isnull().sum()
    duplicate_count = df.duplicated().sum()
    print(f"缺失值总数：{missing_values.sum()}")
    print(f"重复值数量：{duplicate_count}")
    print(f"数据质量：{'✅ 良好' if missing_values.sum() == 0 and duplicate_count == 0 else '⚠️ 需要清理'}")
    
    # (7) 验证统计分析
    print("\\n=== (7) 统计分析验证 ===")
    numeric_columns = ['国内生产总值（十亿美元）', '人口（百万）', '失业率（%）', '人均GDP（美元）']
    desc_stats = df[numeric_columns].describe()
    print("描述性统计（部分）：")
    print(f"GDP平均值：{desc_stats.loc['mean', '国内生产总值（十亿美元）']:,.2f}")
    print(f"失业率平均值：{desc_stats.loc['mean', '失业率（%）']:.2f}%")
    
    # 相关系数验证
    df_corr = df[numeric_columns].copy()
    df_corr.columns = ['GDP', 'Population', 'Unemployment', 'GDP_per_capita']
    correlation_matrix = df_corr.corr()
    print(f"\\nGDP与人口相关系数：{correlation_matrix.loc['GDP', 'Population']:.3f}")
    print(f"GDP与失业率相关系数：{correlation_matrix.loc['GDP', 'Unemployment']:.3f}")
    
    # (8) 验证可视化数据准备
    print("\\n=== (8) 可视化数据验证 ===")
    top10_gdp = df.nlargest(10, '国内生产总值（十亿美元）')
    print(f"GDP前10国家数据准备完成，数据量：{len(top10_gdp)}")
    
    print(f"失业率数据范围：{df['失业率（%）'].min():.1f}% - {df['失业率（%）'].max():.1f}%")
    
    # 汇总验证结果
    print("\\n=== 验证汇总 ===")
    print("✅ 数据读取和索引重置")
    print("✅ 单列和多列数据提取")
    print("✅ 条件筛选功能")
    print("✅ 人均GDP计算公式")
    print("✅ 数据排序功能")
    print("✅ 数据质量检查")
    print("✅ 统计分析功能")
    print("✅ 可视化数据准备")
    
    print(f"\\n数据集概况：")
    print(f"- 包含 {len(df)} 个国家")
    print(f"- GDP最高：{df.loc[df['国内生产总值（十亿美元）'].idxmax(), '国家']}")
    print(f"- 人均GDP最高：{df.loc[df['人均GDP（美元）'].idxmax(), '国家']}")
    print(f"- 失业率最低：{df.loc[df['失业率（%）'].idxmin(), '国家']} ({df['失业率（%）'].min():.1f}%)")
    
    print("\\n所有功能验证完成！")

if __name__ == "__main__":
    test_economic_analyzer()
