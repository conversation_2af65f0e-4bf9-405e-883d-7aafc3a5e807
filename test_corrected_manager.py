# 验证修正后的学生成绩管理系统
def test_corrected_manager():
    # 创建测试字典
    economics_trade = {
        "202210105035": {"姓名": "朱燕柳", "成绩": 85},  # 本人
        "202210137012": {"姓名": "杨阳", "成绩": 94},
        "202210137019": {"姓名": "林华鑫", "成绩": 95},
        "202210137040": {"姓名": "莫诗雅", "成绩": 75},
        "202310137067": {"姓名": "张某某", "成绩": 88},
        "202310137068": {"姓名": "李某某", "成绩": 92},
    }
    
    print("=== 修正后的学生成绩管理系统验证 ===")
    print("初始数据：")
    for sid, info in economics_trade.items():
        print(f"{sid}: {info}")
    
    # (1) 修改张某某的成绩状态为"缺考"
    economics_trade["202310137067"]["成绩"] = "缺考"
    print("\\n(1) 修改张某某成绩为缺考 ✅")
    
    # (2) 将本人(朱燕柳)的成绩修改为100
    economics_trade["202210105035"]["成绩"] = 100
    print("(2) 修改朱燕柳成绩为100 ✅")
    
    # (3) 删除李某某
    del economics_trade["202310137068"]
    print("(3) 删除李某某信息 ✅")
    
    # (4) 遍历输出
    print("\\n(4) 当前学生信息：")
    print(f"{'学号':<15} {'姓名':<8} {'成绩':<6}")
    print("-" * 35)
    for student_id, info in economics_trade.items():
        print(f"{student_id:<15} {info['姓名']:<8} {info['成绩']:<6}")
    
    # (5) 统计总人数
    total_students = len(economics_trade)
    print(f"\\n(5) 总人数：{total_students} 人 ✅")
    
    # (6) 模拟查询朱燕柳
    print("\\n(6) 查询朱燕柳成绩：")
    test_id = "202210105035"
    if test_id in economics_trade:
        info = economics_trade[test_id]
        print(f"学号：{test_id}，姓名：{info['姓名']}，成绩：{info['成绩']}")
    else:
        print("没找到该同学")
    
    # (7) 统计80分以上
    high_score_count = 0
    print("\\n80分以上学生详情：")
    for student_id, info in economics_trade.items():
        if isinstance(info['成绩'], (int, float)) and info['成绩'] >= 80:
            high_score_count += 1
            print(f"  {info['姓名']}: {info['成绩']}分")
    
    print(f"\\n(7) 80分以上学生：{high_score_count} 名 ✅")
    
    print("\\n✅ 朱燕柳同学的成绩已正确修改为100分！")

if __name__ == "__main__":
    test_corrected_manager()
