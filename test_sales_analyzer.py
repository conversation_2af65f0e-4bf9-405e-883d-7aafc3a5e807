# 验证销售数据分析程序
import numpy as np
import matplotlib.pyplot as plt

def test_sales_analyzer():
    print("=== 销售数据分析程序验证 ===")
    
    # 设置随机种子保证数据可复现
    np.random.seed(0)
    
    # (1) 数据准备：创建5x5随机整数矩阵
    sales_data = np.random.randint(10, 101, size=(5, 5))
    
    products = ['产品A', '产品B', '产品C', '产品D', '产品E']
    days = ['第1天', '第2天', '第3天', '第4天', '第5天']
    
    print("\\n(1) 销售数据矩阵验证：")
    print(f"矩阵形状：{sales_data.shape}")
    print(f"数据范围：{np.min(sales_data)} - {np.max(sales_data)}")
    print("销售数据：")
    print(sales_data)
    
    # (2) 销售总额计算
    total_sales = np.sum(sales_data)
    print(f"\\n(2) 总销售额：{total_sales} 件")
    
    # 手动验证
    manual_total = 0
    for i in range(5):
        for j in range(5):
            manual_total += sales_data[i, j]
    print(f"手动计算验证：{manual_total} 件 {'✅' if manual_total == total_sales else '❌'}")
    
    # (3) 日均销售额
    daily_avg_sales = np.mean(sales_data, axis=0)
    print(f"\\n(3) 各产品日均销售量：")
    for i, product in enumerate(products):
        manual_avg = np.sum(sales_data[:, i]) / 5
        print(f"{product}：{daily_avg_sales[i]:.2f} 件/天（验证：{manual_avg:.2f} {'✅' if abs(daily_avg_sales[i] - manual_avg) < 0.01 else '❌'}）")
    
    # (4) 找出畅销产品
    best_product_index = np.argmax(daily_avg_sales)
    best_product_name = products[best_product_index]
    best_product_sales = daily_avg_sales[best_product_index]
    
    print(f"\\n(4) 畅销产品：{best_product_name}，日均销售量：{best_product_sales:.2f} 件/天")
    
    # 验证是否确实是最大值
    is_max = all(best_product_sales >= avg for avg in daily_avg_sales)
    print(f"验证最大值：{'✅' if is_max else '❌'}")
    
    # (5) 销售波动分析
    sales_std = np.std(sales_data, axis=0)
    print(f"\\n(5) 各产品销售波动性（标准差）：")
    for i, product in enumerate(products):
        # 手动计算标准差验证
        manual_std = np.sqrt(np.mean((sales_data[:, i] - daily_avg_sales[i])**2))
        print(f"{product}：{sales_std[i]:.2f}（验证：{manual_std:.2f} {'✅' if abs(sales_std[i] - manual_std) < 0.01 else '❌'}）")
    
    # (6) 数据汇总
    print(f"\\n=== 数据汇总验证 ===")
    print(f"数据维度：{sales_data.shape}")
    print(f"总销售额：{total_sales} 件")
    print(f"日均总销售：{total_sales/5:.2f} 件/天")
    print(f"最佳产品：{best_product_name}")
    print(f"最稳定产品：{products[np.argmin(sales_std)]}（标准差最小）")
    print(f"最波动产品：{products[np.argmax(sales_std)]}（标准差最大）")
    
    # 验证数据范围
    print(f"\\n=== 数据质量验证 ===")
    print(f"所有数据在10-100范围内：{'✅' if np.all((sales_data >= 10) & (sales_data <= 100)) else '❌'}")
    print(f"无缺失值：{'✅' if not np.any(np.isnan(sales_data)) else '❌'}")
    print(f"数据类型正确：{'✅' if sales_data.dtype == np.int32 or sales_data.dtype == np.int64 else '❌'}")
    
    # 显示具体的销售数据表格
    print(f"\\n=== 详细销售数据表 ===")
    print(f"{'天数':<6}", end="")
    for product in products:
        print(f"{product:<8}", end="")
    print("日总计")
    print("-" * 55)
    
    for i, day in enumerate(days):
        print(f"{day:<6}", end="")
        day_total = 0
        for j in range(5):
            print(f"{sales_data[i, j]:<8}", end="")
            day_total += sales_data[i, j]
        print(f"{day_total}")
    
    print("-" * 55)
    print(f"{'总计':<6}", end="")
    for j in range(5):
        col_total = np.sum(sales_data[:, j])
        print(f"{col_total:<8}", end="")
    print(f"{total_sales}")
    
    print("\\n程序验证完成！所有计算结果正确。")

if __name__ == "__main__":
    test_sales_analyzer()
